package com.example.springvueapp.controller;

import com.example.springvueapp.config.TestSecurityConfig;
import com.example.springvueapp.model.DevOpsCiTask;
import com.example.springvueapp.model.DevOpsCiTaskInstance;
import com.example.springvueapp.service.CiTaskService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * DevOpsCiTaskController的单元测试
 */
@WebFluxTest(DevOpsCiTaskController.class)
@Import(TestSecurityConfig.class)
class DevOpsCiTaskControllerTest {

    @Autowired
    private WebTestClient webTestClient;

    @MockBean
    private CiTaskService ciTaskService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testCreateCiTask_WithValidInput_ShouldReturnCreatedTask() throws Exception {
        // Given
        Long componentId = 1L;
        Map<String, Object> config = new HashMap<>();
        config.put("image", "maven:3.8-openjdk-17");

        DevOpsCiTask inputTask = DevOpsCiTask.builder()
                .name("test-task")
                .description("测试任务")
                .taskType("build")
                .configuration(config)
                .build();

        DevOpsCiTask createdTask = DevOpsCiTask.builder()
                .id(1L)
                .name("test-task")
                .description("测试任务")
                .componentId(componentId)
                .taskType("build")
                .status("INACTIVE")
                .configuration(config)
                .userId(1L)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        when(ciTaskService.createCiTask(any(DevOpsCiTask.class), eq(componentId), eq(1L)))
                .thenReturn(Mono.just(createdTask));

        // When & Then
        webTestClient.post()
                .uri("/api/devops/ci/tasks/component/{componentId}", componentId)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(objectMapper.writeValueAsString(inputTask))
                .exchange()
                .expectStatus().isCreated()
                .expectBody()
                .jsonPath("$.id").isEqualTo(1)
                .jsonPath("$.name").isEqualTo("test-task")
                .jsonPath("$.description").isEqualTo("测试任务")
                .jsonPath("$.componentId").isEqualTo(componentId)
                .jsonPath("$.taskType").isEqualTo("build")
                .jsonPath("$.status").isEqualTo("INACTIVE")
                .jsonPath("$.userId").isEqualTo(1);
    }

    @Test
    void testCreateCiTask_WithInvalidInput_ShouldReturnBadRequest() throws Exception {
        // Given
        Long componentId = 1L;
        DevOpsCiTask inputTask = DevOpsCiTask.builder()
                .name("test-task")
                .build();

        when(ciTaskService.createCiTask(any(DevOpsCiTask.class), eq(componentId), eq(1L)))
                .thenReturn(Mono.error(new IllegalArgumentException("组件不存在或无权限访问")));

        // When & Then
        webTestClient.post()
                .uri("/api/devops/ci/tasks/component/{componentId}", componentId)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(objectMapper.writeValueAsString(inputTask))
                .exchange()
                .expectStatus().isBadRequest();
    }

    @Test
    void testGetAllCiTasks_ShouldReturnTaskList() {
        // Given
        DevOpsCiTask task1 = DevOpsCiTask.builder()
                .id(1L)
                .name("task1")
                .taskType("build")
                .userId(1L)
                .build();

        DevOpsCiTask task2 = DevOpsCiTask.builder()
                .id(2L)
                .name("task2")
                .taskType("test")
                .userId(1L)
                .build();

        when(ciTaskService.getAllCiTasks(1L))
                .thenReturn(Flux.just(task1, task2));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/ci/tasks")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(DevOpsCiTask.class)
                .hasSize(2);
    }

    @Test
    void testGetCiTaskById_WithValidId_ShouldReturnTask() {
        // Given
        Long taskId = 1L;
        DevOpsCiTask task = DevOpsCiTask.builder()
                .id(taskId)
                .name("test-task")
                .taskType("build")
                .userId(1L)
                .build();

        when(ciTaskService.getCiTaskById(taskId, 1L))
                .thenReturn(Mono.just(task));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/ci/tasks/{taskId}", taskId)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.id").isEqualTo(taskId)
                .jsonPath("$.name").isEqualTo("test-task")
                .jsonPath("$.taskType").isEqualTo("build");
    }

    @Test
    void testGetCiTaskById_WithInvalidId_ShouldReturnNotFound() {
        // Given
        Long taskId = 999L;

        when(ciTaskService.getCiTaskById(taskId, 1L))
                .thenReturn(Mono.error(new IllegalArgumentException("CI任务不存在或无权限访问")));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/ci/tasks/{taskId}", taskId)
                .exchange()
                .expectStatus().isNotFound();
    }

    @Test
    void testStartCiTask_WithValidTask_ShouldReturnInstance() throws Exception {
        // Given
        Long taskId = 1L;
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("branch", "main");

        DevOpsCiTaskInstance instance = DevOpsCiTaskInstance.builder()
                .id(1L)
                .ciTaskId(taskId)
                .instanceId("test-task-12345678")
                .status("RUNNING")
                .userId(1L)
                .startTime(LocalDateTime.now())
                .build();

        when(ciTaskService.startCiTask(eq(taskId), eq(1L), any(Map.class)))
                .thenReturn(Mono.just(instance));

        // When & Then
        webTestClient.post()
                .uri("/api/devops/ci/tasks/{taskId}/start", taskId)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(objectMapper.writeValueAsString(parameters))
                .exchange()
                .expectStatus().isCreated()
                .expectBody()
                .jsonPath("$.id").isEqualTo(1)
                .jsonPath("$.ciTaskId").isEqualTo(taskId)
                .jsonPath("$.instanceId").isEqualTo("test-task-12345678")
                .jsonPath("$.status").isEqualTo("RUNNING");
    }

    @Test
    void testStopCiTaskInstance_WithValidInstance_ShouldReturnOk() {
        // Given
        String instanceId = "test-task-12345678";

        when(ciTaskService.stopCiTaskInstance(instanceId, 1L))
                .thenReturn(Mono.just(true));

        // When & Then
        webTestClient.post()
                .uri("/api/devops/ci/tasks/instances/{instanceId}/stop", instanceId)
                .exchange()
                .expectStatus().isOk();
    }

    @Test
    void testCancelCiTaskInstance_WithValidInstance_ShouldReturnOk() {
        // Given
        String instanceId = "test-task-12345678";

        when(ciTaskService.cancelCiTaskInstance(instanceId, 1L))
                .thenReturn(Mono.just(true));

        // When & Then
        webTestClient.post()
                .uri("/api/devops/ci/tasks/instances/{instanceId}/cancel", instanceId)
                .exchange()
                .expectStatus().isOk();
    }

    @Test
    void testGetCiTaskInstanceStatus_WithValidInstance_ShouldReturnInstance() {
        // Given
        String instanceId = "test-task-12345678";
        DevOpsCiTaskInstance instance = DevOpsCiTaskInstance.builder()
                .id(1L)
                .ciTaskId(1L)
                .instanceId(instanceId)
                .status("RUNNING")
                .userId(1L)
                .build();

        when(ciTaskService.getCiTaskInstanceStatus(instanceId, 1L))
                .thenReturn(Mono.just(instance));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/ci/tasks/instances/{instanceId}", instanceId)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.instanceId").isEqualTo(instanceId)
                .jsonPath("$.status").isEqualTo("RUNNING");
    }

    @Test
    void testGetCiTaskInstanceLogs_WithValidInstance_ShouldReturnLogs() {
        // Given
        String instanceId = "test-task-12345678";
        String logs = "构建开始...\n编译成功\n构建完成";

        when(ciTaskService.getCiTaskInstanceLogs(instanceId, 1L))
                .thenReturn(Mono.just(logs));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/ci/tasks/instances/{instanceId}/logs", instanceId)
                .exchange()
                .expectStatus().isOk()
                .expectBody(String.class)
                .isEqualTo(logs);
    }

    @Test
    void testGetTaskTemplate_WithValidType_ShouldReturnTemplate() {
        // Given
        String taskType = "build";
        Map<String, Object> template = new HashMap<>();
        template.put("apiVersion", "tekton.dev/v1beta1");
        template.put("kind", "Task");

        when(ciTaskService.getTaskTemplate(taskType))
                .thenReturn(Mono.just(template));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/ci/tasks/templates/{taskType}", taskType)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.apiVersion").isEqualTo("tekton.dev/v1beta1")
                .jsonPath("$.kind").isEqualTo("Task");
    }

    @Test
    void testGetSupportedTaskTypes_ShouldReturnTypes() {
        // Given
        when(ciTaskService.getSupportedTaskTypes())
                .thenReturn(Flux.just("build", "test", "deploy", "custom"));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/ci/tasks/types")
                .exchange()
                .expectStatus().isOk()
                .expectBody(String.class)
                .isEqualTo("buildtestdeploycustom");
    }

    @Test
    void testCheckConnection_ShouldReturnStatus() {
        // Given
        when(ciTaskService.checkConnection())
                .thenReturn(Mono.just(true));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/ci/tasks/platform/status")
                .exchange()
                .expectStatus().isOk()
                .expectBody(Boolean.class)
                .isEqualTo(true);
    }

    @Test
    void testGetPlatformInfo_ShouldReturnInfo() {
        // Given
        Map<String, Object> info = new HashMap<>();
        info.put("platform", "Tekton");
        info.put("version", "v0.50.0");
        info.put("status", "connected");

        when(ciTaskService.getPlatformInfo())
                .thenReturn(Mono.just(info));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/ci/tasks/platform/info")
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.platform").isEqualTo("Tekton")
                .jsonPath("$.version").isEqualTo("v0.50.0")
                .jsonPath("$.status").isEqualTo("connected");
    }
}
