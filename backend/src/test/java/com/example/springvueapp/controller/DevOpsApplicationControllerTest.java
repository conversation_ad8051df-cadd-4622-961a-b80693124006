package com.example.springvueapp.controller;

import com.example.springvueapp.model.DevOpsApplication;
import com.example.springvueapp.service.DevOpsApplicationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@WebFluxTest(DevOpsApplicationController.class)
@Import(com.example.springvueapp.config.TestSecurityConfig.class)
class DevOpsApplicationControllerTest {

    @Autowired
    private WebTestClient webTestClient;

    @MockBean
    private DevOpsApplicationService applicationService;

    private DevOpsApplication testApplication;

    @BeforeEach
    void setUp() {
        testApplication = new DevOpsApplication();
        testApplication.setId(1L);
        testApplication.setName("测试应用");
        testApplication.setDescription("这是一个测试应用");
        testApplication.setProjectId(1L);
        testApplication.setStatus("ACTIVE");
        testApplication.setUserId(1L);
        testApplication.setCreatedAt(LocalDateTime.now());
        testApplication.setUpdatedAt(LocalDateTime.now());
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void createApplication_ShouldReturnCreatedApplication() throws Exception {
        // Given
        DevOpsApplication newApplication = new DevOpsApplication();
        newApplication.setName("新应用");
        newApplication.setDescription("新应用描述");
        newApplication.setStatus("ACTIVE");

        when(applicationService.createApplication(any(DevOpsApplication.class), eq(1L), anyLong()))
                .thenReturn(Mono.just(testApplication));

        // When & Then
        webTestClient.post()
                .uri("/api/devops/applications/project/1")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(newApplication)
                .exchange()
                .expectStatus().isCreated()
                .expectBody(DevOpsApplication.class)
                .value(application -> {
                    assert application.getId().equals(1L);
                    assert application.getName().equals("测试应用");
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void getAllApplications_ShouldReturnApplicationList() {
        // Given
        when(applicationService.getAllApplications(anyLong()))
                .thenReturn(Flux.just(testApplication));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/applications")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(DevOpsApplication.class)
                .hasSize(1)
                .value(applications -> {
                    assert applications.get(0).getName().equals("测试应用");
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void getApplicationsByProject_ShouldReturnApplicationList() {
        // Given
        when(applicationService.getApplicationsByProject(eq(1L), anyLong()))
                .thenReturn(Flux.just(testApplication));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/applications/project/1")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(DevOpsApplication.class)
                .hasSize(1)
                .value(applications -> {
                    assert applications.get(0).getProjectId().equals(1L);
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void getApplicationById_ShouldReturnApplication() {
        // Given
        when(applicationService.getApplicationById(eq(1L), anyLong()))
                .thenReturn(Mono.just(testApplication));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/applications/1")
                .exchange()
                .expectStatus().isOk()
                .expectBody(DevOpsApplication.class)
                .value(application -> {
                    assert application.getId().equals(1L);
                    assert application.getName().equals("测试应用");
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void getApplicationById_WhenNotFound_ShouldReturn404() {
        // Given
        when(applicationService.getApplicationById(eq(999L), anyLong()))
                .thenReturn(Mono.error(new IllegalArgumentException("Application not found")));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/applications/999")
                .exchange()
                .expectStatus().isNotFound();
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void updateApplication_ShouldReturnUpdatedApplication() throws Exception {
        // Given
        DevOpsApplication updatedApplication = new DevOpsApplication();
        updatedApplication.setName("更新的应用");
        updatedApplication.setDescription("更新的描述");
        updatedApplication.setStatus("INACTIVE");

        DevOpsApplication returnApplication = new DevOpsApplication();
        returnApplication.setId(1L);
        returnApplication.setName("更新的应用");
        returnApplication.setDescription("更新的描述");
        returnApplication.setStatus("INACTIVE");
        returnApplication.setUserId(1L);

        when(applicationService.updateApplication(eq(1L), any(DevOpsApplication.class), anyLong()))
                .thenReturn(Mono.just(returnApplication));

        // When & Then
        webTestClient.put()
                .uri("/api/devops/applications/1")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(updatedApplication)
                .exchange()
                .expectStatus().isOk()
                .expectBody(DevOpsApplication.class)
                .value(application -> {
                    assert application.getName().equals("更新的应用");
                    assert application.getStatus().equals("INACTIVE");
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void deleteApplication_ShouldReturnNoContent() {
        // Given
        when(applicationService.deleteApplication(eq(1L), anyLong()))
                .thenReturn(Mono.just(true));

        // When & Then
        webTestClient.delete()
                .uri("/api/devops/applications/1")
                .exchange()
                .expectStatus().isNoContent();
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void deleteApplication_WhenNotFound_ShouldReturn404() {
        // Given
        when(applicationService.deleteApplication(eq(999L), anyLong()))
                .thenReturn(Mono.error(new IllegalArgumentException("Application not found")));

        // When & Then
        webTestClient.delete()
                .uri("/api/devops/applications/999")
                .exchange()
                .expectStatus().isNotFound();
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void searchApplications_ShouldReturnMatchingApplications() {
        // Given
        when(applicationService.searchApplications(anyLong(), eq("测试")))
                .thenReturn(Flux.just(testApplication));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/applications/search?keyword=测试")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(DevOpsApplication.class)
                .hasSize(1)
                .value(applications -> {
                    assert applications.get(0).getName().equals("测试应用");
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void searchApplicationsInProject_ShouldReturnMatchingApplications() {
        // Given
        when(applicationService.searchApplicationsInProject(eq(1L), anyLong(), eq("测试")))
                .thenReturn(Flux.just(testApplication));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/applications/project/1/search?keyword=测试")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(DevOpsApplication.class)
                .hasSize(1)
                .value(applications -> {
                    assert applications.get(0).getName().equals("测试应用");
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void countApplications_ShouldReturnCount() {
        // Given
        when(applicationService.countApplications(anyLong()))
                .thenReturn(Mono.just(3L));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/applications/count")
                .exchange()
                .expectStatus().isOk()
                .expectBody(Long.class)
                .value(count -> {
                    assert count.equals(3L);
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void countApplicationsByProject_ShouldReturnCount() {
        // Given
        when(applicationService.countApplicationsByProject(eq(1L), anyLong()))
                .thenReturn(Mono.just(2L));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/applications/project/1/count")
                .exchange()
                .expectStatus().isOk()
                .expectBody(Long.class)
                .value(count -> {
                    assert count.equals(2L);
                });
    }


}
