package com.example.springvueapp.mapper;

import com.example.springvueapp.entity.DevOpsCiTaskEntity;
import com.example.springvueapp.model.DevOpsCiTask;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DevOpsCiTaskMapper的单元测试
 */
@ExtendWith(MockitoExtension.class)
class DevOpsCiTaskMapperTest {

    private DevOpsCiTaskMapper mapper;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        mapper = new DevOpsCiTaskMapper(objectMapper);
    }

    @Test
    void testToDto_WithValidEntity_ShouldReturnDto() {
        // Given
        LocalDateTime now = LocalDateTime.now();
        Map<String, Object> config = new HashMap<>();
        config.put("image", "maven:3.8-openjdk-17");
        config.put("script", "mvn clean compile");

        DevOpsCiTaskEntity entity = DevOpsCiTaskEntity.builder()
                .id(1L)
                .name("test-task")
                .description("测试任务")
                .componentId(100L)
                .version("1.0.0")
                .taskType("build")
                .status("ACTIVE")
                .configuration("{\"image\":\"maven:3.8-openjdk-17\",\"script\":\"mvn clean compile\"}")
                .userId(10L)
                .createdAt(now)
                .updatedAt(now)
                .build();

        // When
        DevOpsCiTask dto = mapper.toDto(entity);

        // Then
        assertNotNull(dto);
        assertEquals(1L, dto.getId());
        assertEquals("test-task", dto.getName());
        assertEquals("测试任务", dto.getDescription());
        assertEquals(100L, dto.getComponentId());
        assertEquals("1.0.0", dto.getVersion());
        assertEquals("build", dto.getTaskType());
        assertEquals("ACTIVE", dto.getStatus());
        assertEquals(10L, dto.getUserId());
        assertEquals(now, dto.getCreatedAt());
        assertEquals(now, dto.getUpdatedAt());
        
        assertNotNull(dto.getConfiguration());
        assertEquals("maven:3.8-openjdk-17", dto.getConfiguration().get("image"));
        assertEquals("mvn clean compile", dto.getConfiguration().get("script"));
    }

    @Test
    void testToDto_WithNullEntity_ShouldReturnNull() {
        // When
        DevOpsCiTask dto = mapper.toDto(null);

        // Then
        assertNull(dto);
    }

    @Test
    void testToDto_WithEmptyConfiguration_ShouldReturnEmptyMap() {
        // Given
        DevOpsCiTaskEntity entity = DevOpsCiTaskEntity.builder()
                .id(1L)
                .name("test-task")
                .configuration("")
                .build();

        // When
        DevOpsCiTask dto = mapper.toDto(entity);

        // Then
        assertNotNull(dto);
        assertNotNull(dto.getConfiguration());
        assertTrue(dto.getConfiguration().isEmpty());
    }

    @Test
    void testToEntity_WithValidDto_ShouldReturnEntity() {
        // Given
        LocalDateTime now = LocalDateTime.now();
        Map<String, Object> config = new HashMap<>();
        config.put("image", "maven:3.8-openjdk-17");
        config.put("script", "mvn clean compile");

        DevOpsCiTask dto = DevOpsCiTask.builder()
                .id(1L)
                .name("test-task")
                .description("测试任务")
                .componentId(100L)
                .version("1.0.0")
                .taskType("build")
                .status("ACTIVE")
                .configuration(config)
                .userId(10L)
                .createdAt(now)
                .updatedAt(now)
                .build();

        // When
        DevOpsCiTaskEntity entity = mapper.toEntity(dto);

        // Then
        assertNotNull(entity);
        assertEquals(1L, entity.getId());
        assertEquals("test-task", entity.getName());
        assertEquals("测试任务", entity.getDescription());
        assertEquals(100L, entity.getComponentId());
        assertEquals("1.0.0", entity.getVersion());
        assertEquals("build", entity.getTaskType());
        assertEquals("ACTIVE", entity.getStatus());
        assertEquals(10L, entity.getUserId());
        assertEquals(now, entity.getCreatedAt());
        assertEquals(now, entity.getUpdatedAt());
        
        assertNotNull(entity.getConfiguration());
        assertTrue(entity.getConfiguration().contains("maven:3.8-openjdk-17"));
        assertTrue(entity.getConfiguration().contains("mvn clean compile"));
    }

    @Test
    void testToEntity_WithNullDto_ShouldReturnNull() {
        // When
        DevOpsCiTaskEntity entity = mapper.toEntity(null);

        // Then
        assertNull(entity);
    }

    @Test
    void testToNewEntity_WithValidDto_ShouldReturnNewEntity() {
        // Given
        Map<String, Object> config = new HashMap<>();
        config.put("image", "maven:3.8-openjdk-17");

        DevOpsCiTask dto = DevOpsCiTask.builder()
                .name("new-task")
                .description("新任务")
                .version("1.0.0")
                .taskType("build")
                .configuration(config)
                .build();

        Long componentId = 100L;
        Long userId = 10L;

        // When
        DevOpsCiTaskEntity entity = mapper.toNewEntity(dto, componentId, userId);

        // Then
        assertNotNull(entity);
        assertNull(entity.getId()); // 新实体不应该有ID
        assertEquals("new-task", entity.getName());
        assertEquals("新任务", entity.getDescription());
        assertEquals(componentId, entity.getComponentId());
        assertEquals("1.0.0", entity.getVersion());
        assertEquals("build", entity.getTaskType());
        assertEquals("INACTIVE", entity.getStatus()); // 默认状态
        assertEquals(userId, entity.getUserId());
        assertNotNull(entity.getCreatedAt());
        assertNotNull(entity.getUpdatedAt());
        assertNotNull(entity.getConfiguration());
    }

    @Test
    void testToNewEntity_WithNullStatus_ShouldSetDefaultStatus() {
        // Given
        DevOpsCiTask dto = DevOpsCiTask.builder()
                .name("new-task")
                .taskType("build")
                .build();

        // When
        DevOpsCiTaskEntity entity = mapper.toNewEntity(dto, 100L, 10L);

        // Then
        assertNotNull(entity);
        assertEquals("INACTIVE", entity.getStatus());
    }

    @Test
    void testToUpdateEntity_WithValidDto_ShouldReturnUpdatedEntity() {
        // Given
        LocalDateTime originalTime = LocalDateTime.now().minusHours(1);
        DevOpsCiTaskEntity existingEntity = DevOpsCiTaskEntity.builder()
                .id(1L)
                .name("old-task")
                .description("旧任务")
                .componentId(100L)
                .version("1.0.0")
                .taskType("build")
                .status("INACTIVE")
                .configuration("{\"old\":\"config\"}")
                .userId(10L)
                .createdAt(originalTime)
                .updatedAt(originalTime)
                .build();

        Map<String, Object> newConfig = new HashMap<>();
        newConfig.put("new", "config");

        DevOpsCiTask dto = DevOpsCiTask.builder()
                .name("updated-task")
                .description("更新任务")
                .version("2.0.0")
                .status("ACTIVE")
                .configuration(newConfig)
                .build();

        // When
        DevOpsCiTaskEntity updatedEntity = mapper.toUpdateEntity(dto, existingEntity);

        // Then
        assertNotNull(updatedEntity);
        assertEquals(1L, updatedEntity.getId()); // ID应该保持不变
        assertEquals("updated-task", updatedEntity.getName());
        assertEquals("更新任务", updatedEntity.getDescription());
        assertEquals(100L, updatedEntity.getComponentId()); // 组件ID应该保持不变
        assertEquals("2.0.0", updatedEntity.getVersion());
        assertEquals("build", updatedEntity.getTaskType()); // 未更新的字段保持原值
        assertEquals("ACTIVE", updatedEntity.getStatus());
        assertEquals(10L, updatedEntity.getUserId()); // 用户ID应该保持不变
        assertEquals(originalTime, updatedEntity.getCreatedAt()); // 创建时间应该保持不变
        assertTrue(updatedEntity.getUpdatedAt().isAfter(originalTime)); // 更新时间应该更新
        assertTrue(updatedEntity.getConfiguration().contains("new"));
    }

    @Test
    void testToUpdateEntity_WithPartialDto_ShouldKeepExistingValues() {
        // Given
        DevOpsCiTaskEntity existingEntity = DevOpsCiTaskEntity.builder()
                .id(1L)
                .name("existing-task")
                .description("现有任务")
                .componentId(100L)
                .version("1.0.0")
                .taskType("build")
                .status("ACTIVE")
                .configuration("{\"existing\":\"config\"}")
                .userId(10L)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        DevOpsCiTask dto = DevOpsCiTask.builder()
                .name("updated-name")
                // 只更新名称，其他字段为null
                .build();

        // When
        DevOpsCiTaskEntity updatedEntity = mapper.toUpdateEntity(dto, existingEntity);

        // Then
        assertNotNull(updatedEntity);
        assertEquals("updated-name", updatedEntity.getName()); // 更新的字段
        assertEquals("现有任务", updatedEntity.getDescription()); // 保持原值
        assertEquals("1.0.0", updatedEntity.getVersion()); // 保持原值
        assertEquals("build", updatedEntity.getTaskType()); // 保持原值
        assertEquals("ACTIVE", updatedEntity.getStatus()); // 保持原值
        assertEquals("{\"existing\":\"config\"}", updatedEntity.getConfiguration()); // 保持原值
    }

    @Test
    void testToUpdateEntity_WithNullDto_ShouldReturnNull() {
        // Given
        DevOpsCiTaskEntity existingEntity = DevOpsCiTaskEntity.builder()
                .id(1L)
                .name("existing-task")
                .build();

        // When
        DevOpsCiTaskEntity result = mapper.toUpdateEntity(null, existingEntity);

        // Then
        assertNull(result);
    }

    @Test
    void testToUpdateEntity_WithNullExistingEntity_ShouldReturnNull() {
        // Given
        DevOpsCiTask dto = DevOpsCiTask.builder()
                .name("new-task")
                .build();

        // When
        DevOpsCiTaskEntity result = mapper.toUpdateEntity(dto, null);

        // Then
        assertNull(result);
    }
}
