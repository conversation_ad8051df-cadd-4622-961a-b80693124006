package com.example.springvueapp.integration;

import com.example.springvueapp.service.ExecutorService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import reactor.test.StepVerifier;

import java.util.HashMap;
import java.util.Map;

/**
 * DevOps管理模块的综合集成测试
 * 专注于测试ExecutorService的集成功能
 */
@SpringBootTest
@ActiveProfiles("test")
class DevOpsIntegrationTest {

    @Autowired
    private ExecutorService executorService;

    @Test
    void testExecutorServiceBasicOperations() {
        // 测试执行器服务的基本功能
        String taskName = "deploy-test-app";
        String deploymentCommand = "kubectl apply -f deployment.yaml";
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("namespace", "default");
        parameters.put("image", "test-app:v1.0.0");

        // 提交部署任务
        StepVerifier.create(executorService.submitDeploymentTask(taskName, deploymentCommand, parameters))
                .expectNextMatches(result -> {
                    return result.containsKey("taskId") &&
                           result.get("taskName").equals(taskName) &&
                           result.get("status").equals("SUBMITTED");
                })
                .verifyComplete();

        // 测试连接状态
        StepVerifier.create(executorService.checkConnection())
                .expectNext(true)
                .verifyComplete();

        // 测试获取执行器信息
        StepVerifier.create(executorService.getExecutorInfo())
                .expectNextMatches(info -> {
                    return "Tekton".equals(info.get("executor")) &&
                           "connected".equals(info.get("status"));
                })
                .verifyComplete();

        // 测试命令验证
        StepVerifier.create(executorService.validateDeploymentCommand(deploymentCommand))
                .expectNext(true)
                .verifyComplete();

        // 测试支持的命令类型
        StepVerifier.create(executorService.getSupportedCommandTypes())
                .expectNextCount(5)
                .verifyComplete();
    }



    @Test
    void testExecutorServiceTaskManagement() {
        // 测试任务管理功能
        String taskId = "test-task-12345";

        // 测试获取任务状态
        StepVerifier.create(executorService.getTaskStatus(taskId))
                .expectNextMatches(status -> {
                    return status.containsKey("taskId") &&
                           status.containsKey("status") &&
                           status.containsKey("progress");
                })
                .verifyComplete();

        // 测试获取任务日志
        StepVerifier.create(executorService.getTaskLogs(taskId))
                .expectNextMatches(logs -> logs.contains("部署"))
                .verifyComplete();

        // 测试获取任务结果
        StepVerifier.create(executorService.getTaskResult(taskId))
                .expectNextMatches(result -> {
                    return result.containsKey("taskId") &&
                           result.containsKey("status") &&
                           result.containsKey("exitCode");
                })
                .verifyComplete();

        // 测试任务控制操作
        StepVerifier.create(executorService.stopTask(taskId))
                .expectNext(true)
                .verifyComplete();

        StepVerifier.create(executorService.cancelTask(taskId))
                .expectNext(true)
                .verifyComplete();

        StepVerifier.create(executorService.pauseTask(taskId))
                .expectNext(true)
                .verifyComplete();

        StepVerifier.create(executorService.resumeTask(taskId))
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    void testExecutorServiceNamespaceOperations() {
        String testNamespace = "test-namespace";

        // 测试创建命名空间
        StepVerifier.create(executorService.createNamespace(testNamespace))
                .expectNext(true)
                .verifyComplete();

        // 测试列出命名空间
        StepVerifier.create(executorService.listNamespaces())
                .expectNextCount(4) // 预期有4个命名空间
                .verifyComplete();

        // 测试列出任务
        StepVerifier.create(executorService.listTasks(testNamespace))
                .expectNextCount(2) // 预期有2个任务
                .verifyComplete();

        // 测试清理任务
        StepVerifier.create(executorService.cleanupCompletedTasks(testNamespace, 5))
                .expectNext(3) // 预期清理3个任务
                .verifyComplete();

        // 测试删除命名空间
        StepVerifier.create(executorService.deleteNamespace(testNamespace))
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    void testExecutorServiceMonitoring() {
        String taskId = "monitor-test-task";

        // 测试监控任务进度
        StepVerifier.create(executorService.monitorTaskProgress(taskId))
                .expectNextCount(3) // 预期有3个进度更新
                .verifyComplete();

        // 测试获取任务指标
        StepVerifier.create(executorService.getTaskMetrics(taskId))
                .expectNextMatches(metrics -> {
                    return metrics.containsKey("taskId") &&
                           metrics.containsKey("cpuUsage") &&
                           metrics.containsKey("memoryUsage");
                })
                .verifyComplete();

        // 测试重试任务
        StepVerifier.create(executorService.retryTask(taskId))
                .expectNextMatches(result -> {
                    return result.containsKey("originalTaskId") &&
                           result.containsKey("newTaskId") &&
                           "RETRYING".equals(result.get("status"));
                })
                .verifyComplete();
    }
}
