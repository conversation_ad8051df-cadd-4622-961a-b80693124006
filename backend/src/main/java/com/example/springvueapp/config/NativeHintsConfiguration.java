package com.example.springvueapp.config;

import com.example.springvueapp.mcp.model.*;
import com.example.springvueapp.model.*;
import com.example.springvueapp.entity.*;
import com.example.springvueapp.sandbox.*;
import com.github.dockerjava.api.model.*;
import com.github.dockerjava.api.command.CreateContainerResponse;
import org.springframework.aot.hint.annotation.RegisterReflectionForBinding;
import org.springframework.context.annotation.Configuration;

/**
 * Spring Boot Native Hints配置
 * 为GraalVM Native Image提供反射和序列化提示
 */
@Configuration
@RegisterReflectionForBinding({
    // 用户相关模型
    User.class,
    UserEntity.class,

    // MCP服务器配置相关
    McpServerConfiguration.class,
    McpServerConfigurationEntity.class,
    McpServerInstance.class,
    McpServerInstanceEntity.class,

    // MCP协议模型
    McpTool.class,
    McpServerInfo.class,
    McpToolCallRequest.class,
    McpToolCallResponse.class,
    McpToolCallResponse.McpContent.class,
    McpInitializeRequest.class,
    McpInitializeRequest.McpClientInfo.class,
    McpInitializeResponse.class,
    McpSseSession.class,

    // 沙箱相关模型
    SandboxConfig.class,
    SandboxResourceUsage.class,
    ResourceLimits.class,
    NetworkConfig.class,
    VolumeMount.class,
    HostEntry.class,

    // DevOps产品管理相关实体
    DevOpsProjectEntity.class,
    DevOpsApplicationEntity.class,
    DevOpsComponentEntity.class,
    DevOpsResourceEntity.class,

    // DevOps产品管理相关DTO
    DevOpsProject.class,
    DevOpsApplication.class,
    DevOpsComponent.class,
    DevOpsResource.class,

    // Docker Java API模型（项目特定需要的）
    Container.class,
    Image.class,
    Statistics.class,
    CreateContainerResponse.class,

    // 枚举类
    SandboxStatus.class,
    McpSseSession.SessionStatus.class
})
public class NativeHintsConfiguration {
    
    // Spring Boot 3.x会自动处理大部分配置
    // 这里只需要注册项目特定的类型
}
