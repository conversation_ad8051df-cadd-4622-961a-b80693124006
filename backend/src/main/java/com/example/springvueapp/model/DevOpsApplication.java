package com.example.springvueapp.model;

import java.time.LocalDateTime;

/**
 * DevOps应用的DTO类
 * 与前端TypeScript接口保持一致，用于API交互
 */
public class DevOpsApplication {

    private Long id;

    private String name;

    private String description;

    private Long projectId;

    private String status;

    private Long userId;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    public DevOpsApplication() {
    }

    public DevOpsApplication(Long id, String name, String description, Long projectId,
                            String status, Long userId, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.projectId = projectId;
        this.status = status;
        this.userId = userId;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "DevOpsApplication{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", projectId=" + projectId +
                ", status='" + status + '\'' +
                ", userId=" + userId +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }

    /**
     * DevOpsApplication 的构建器类
     */
    public static class Builder {
        private DevOpsApplication application = new DevOpsApplication();

        public Builder id(Long id) {
            application.setId(id);
            return this;
        }

        public Builder name(String name) {
            application.setName(name);
            return this;
        }

        public Builder description(String description) {
            application.setDescription(description);
            return this;
        }

        public Builder projectId(Long projectId) {
            application.setProjectId(projectId);
            return this;
        }

        public Builder status(String status) {
            application.setStatus(status);
            return this;
        }

        public Builder userId(Long userId) {
            application.setUserId(userId);
            return this;
        }

        public Builder createdAt(LocalDateTime createdAt) {
            application.setCreatedAt(createdAt);
            return this;
        }

        public Builder updatedAt(LocalDateTime updatedAt) {
            application.setUpdatedAt(updatedAt);
            return this;
        }

        public DevOpsApplication build() {
            return application;
        }
    }

    public static Builder builder() {
        return new Builder();
    }
}
