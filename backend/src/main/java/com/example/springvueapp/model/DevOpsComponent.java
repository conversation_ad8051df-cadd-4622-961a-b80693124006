package com.example.springvueapp.model;

import java.time.LocalDateTime;

/**
 * DevOps组件的DTO类
 * 与前端TypeScript接口保持一致，用于API交互
 * 对应Maven Parent模块概念
 */
public class DevOpsComponent {

    private Long id;

    private String name;

    private String description;

    private Long applicationId;

    private String repositoryUrl;

    private String repositoryType;

    private String status;

    private Long userId;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    public DevOpsComponent() {
    }

    public DevOpsComponent(Long id, String name, String description, Long applicationId,
                          String repositoryUrl, String repositoryType, String status,
                          Long userId, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.applicationId = applicationId;
        this.repositoryUrl = repositoryUrl;
        this.repositoryType = repositoryType;
        this.status = status;
        this.userId = userId;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Long applicationId) {
        this.applicationId = applicationId;
    }

    public String getRepositoryUrl() {
        return repositoryUrl;
    }

    public void setRepositoryUrl(String repositoryUrl) {
        this.repositoryUrl = repositoryUrl;
    }

    public String getRepositoryType() {
        return repositoryType;
    }

    public void setRepositoryType(String repositoryType) {
        this.repositoryType = repositoryType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "DevOpsComponent{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", applicationId=" + applicationId +
                ", repositoryUrl='" + repositoryUrl + '\'' +
                ", repositoryType='" + repositoryType + '\'' +
                ", status='" + status + '\'' +
                ", userId=" + userId +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }

    /**
     * DevOpsComponent 的构建器类
     */
    public static class Builder {
        private DevOpsComponent component = new DevOpsComponent();

        public Builder id(Long id) {
            component.setId(id);
            return this;
        }

        public Builder name(String name) {
            component.setName(name);
            return this;
        }

        public Builder description(String description) {
            component.setDescription(description);
            return this;
        }

        public Builder applicationId(Long applicationId) {
            component.setApplicationId(applicationId);
            return this;
        }

        public Builder repositoryUrl(String repositoryUrl) {
            component.setRepositoryUrl(repositoryUrl);
            return this;
        }

        public Builder repositoryType(String repositoryType) {
            component.setRepositoryType(repositoryType);
            return this;
        }

        public Builder status(String status) {
            component.setStatus(status);
            return this;
        }

        public Builder userId(Long userId) {
            component.setUserId(userId);
            return this;
        }

        public Builder createdAt(LocalDateTime createdAt) {
            component.setCreatedAt(createdAt);
            return this;
        }

        public Builder updatedAt(LocalDateTime updatedAt) {
            component.setUpdatedAt(updatedAt);
            return this;
        }

        public DevOpsComponent build() {
            return component;
        }
    }

    public static Builder builder() {
        return new Builder();
    }
}
