package com.example.springvueapp.model;

import java.time.LocalDateTime;

/**
 * DevOps项目的DTO类
 * 与前端TypeScript接口保持一致，用于API交互
 */
public class DevOpsProject {

    private Long id;

    private String name;

    private String description;

    private String status;

    private Long userId;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    public DevOpsProject() {
    }

    public DevOpsProject(Long id, String name, String description, String status,
                        Long userId, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.status = status;
        this.userId = userId;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "DevOpsProject{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", status='" + status + '\'' +
                ", userId=" + userId +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }

    /**
     * DevOpsProject 的构建器类
     */
    public static class Builder {
        private DevOpsProject project = new DevOpsProject();

        public Builder id(Long id) {
            project.setId(id);
            return this;
        }

        public Builder name(String name) {
            project.setName(name);
            return this;
        }

        public Builder description(String description) {
            project.setDescription(description);
            return this;
        }

        public Builder status(String status) {
            project.setStatus(status);
            return this;
        }

        public Builder userId(Long userId) {
            project.setUserId(userId);
            return this;
        }

        public Builder createdAt(LocalDateTime createdAt) {
            project.setCreatedAt(createdAt);
            return this;
        }

        public Builder updatedAt(LocalDateTime updatedAt) {
            project.setUpdatedAt(updatedAt);
            return this;
        }

        public DevOpsProject build() {
            return project;
        }
    }

    public static Builder builder() {
        return new Builder();
    }
}
