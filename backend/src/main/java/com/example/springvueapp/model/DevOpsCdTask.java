package com.example.springvueapp.model;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * DevOps CD任务的DTO类
 * 与前端TypeScript接口保持一致，用于API交互
 */
public class DevOpsCdTask {

    private Long id;

    private String name;

    private String description;

    private Long applicationId;

    private Map<String, String> componentVersions; // 组件版本信息

    private String status;

    private Map<String, Object> configuration; // 配置信息

    private Long userId;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    public DevOpsCdTask() {
    }

    public DevOpsCdTask(Long id, String name, String description, Long applicationId,
                       Map<String, String> componentVersions, String status, Map<String, Object> configuration,
                       Long userId, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.applicationId = applicationId;
        this.componentVersions = componentVersions;
        this.status = status;
        this.configuration = configuration;
        this.userId = userId;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Long applicationId) {
        this.applicationId = applicationId;
    }

    public Map<String, String> getComponentVersions() {
        return componentVersions;
    }

    public void setComponentVersions(Map<String, String> componentVersions) {
        this.componentVersions = componentVersions;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Map<String, Object> getConfiguration() {
        return configuration;
    }

    public void setConfiguration(Map<String, Object> configuration) {
        this.configuration = configuration;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "DevOpsCdTask{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", applicationId=" + applicationId +
                ", componentVersions=" + componentVersions +
                ", status='" + status + '\'' +
                ", configuration=" + configuration +
                ", userId=" + userId +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }

    /**
     * DevOpsCdTask 的构建器类
     */
    public static class Builder {
        private DevOpsCdTask cdTask = new DevOpsCdTask();

        public Builder id(Long id) {
            cdTask.setId(id);
            return this;
        }

        public Builder name(String name) {
            cdTask.setName(name);
            return this;
        }

        public Builder description(String description) {
            cdTask.setDescription(description);
            return this;
        }

        public Builder applicationId(Long applicationId) {
            cdTask.setApplicationId(applicationId);
            return this;
        }

        public Builder componentVersions(Map<String, String> componentVersions) {
            cdTask.setComponentVersions(componentVersions);
            return this;
        }

        public Builder status(String status) {
            cdTask.setStatus(status);
            return this;
        }

        public Builder configuration(Map<String, Object> configuration) {
            cdTask.setConfiguration(configuration);
            return this;
        }

        public Builder userId(Long userId) {
            cdTask.setUserId(userId);
            return this;
        }

        public Builder createdAt(LocalDateTime createdAt) {
            cdTask.setCreatedAt(createdAt);
            return this;
        }

        public Builder updatedAt(LocalDateTime updatedAt) {
            cdTask.setUpdatedAt(updatedAt);
            return this;
        }

        public DevOpsCdTask build() {
            return cdTask;
        }
    }

    public static Builder builder() {
        return new Builder();
    }
}
