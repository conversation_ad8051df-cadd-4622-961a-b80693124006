package com.example.springvueapp.model;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * DevOps CI任务实例的DTO类
 * 与前端TypeScript接口保持一致，用于API交互
 */
public class DevOpsCiTaskInstance {

    private Long id;

    private Long ciTaskId;

    private String instanceId;

    private String status;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private String logs;

    private Map<String, Object> resultData; // 执行结果数据

    private String errorMessage;

    private Long userId;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    public DevOpsCiTaskInstance() {
    }

    public DevOpsCiTaskInstance(Long id, Long ciTaskId, String instanceId, String status,
                               LocalDateTime startTime, LocalDateTime endTime, String logs,
                               Map<String, Object> resultData, String errorMessage, Long userId,
                               LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.ciTaskId = ciTaskId;
        this.instanceId = instanceId;
        this.status = status;
        this.startTime = startTime;
        this.endTime = endTime;
        this.logs = logs;
        this.resultData = resultData;
        this.errorMessage = errorMessage;
        this.userId = userId;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCiTaskId() {
        return ciTaskId;
    }

    public void setCiTaskId(Long ciTaskId) {
        this.ciTaskId = ciTaskId;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getLogs() {
        return logs;
    }

    public void setLogs(String logs) {
        this.logs = logs;
    }

    public Map<String, Object> getResultData() {
        return resultData;
    }

    public void setResultData(Map<String, Object> resultData) {
        this.resultData = resultData;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "DevOpsCiTaskInstance{" +
                "id=" + id +
                ", ciTaskId=" + ciTaskId +
                ", instanceId='" + instanceId + '\'' +
                ", status='" + status + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", logs='" + logs + '\'' +
                ", resultData=" + resultData +
                ", errorMessage='" + errorMessage + '\'' +
                ", userId=" + userId +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }

    /**
     * DevOpsCiTaskInstance 的构建器类
     */
    public static class Builder {
        private DevOpsCiTaskInstance instance = new DevOpsCiTaskInstance();

        public Builder id(Long id) {
            instance.setId(id);
            return this;
        }

        public Builder ciTaskId(Long ciTaskId) {
            instance.setCiTaskId(ciTaskId);
            return this;
        }

        public Builder instanceId(String instanceId) {
            instance.setInstanceId(instanceId);
            return this;
        }

        public Builder status(String status) {
            instance.setStatus(status);
            return this;
        }

        public Builder startTime(LocalDateTime startTime) {
            instance.setStartTime(startTime);
            return this;
        }

        public Builder endTime(LocalDateTime endTime) {
            instance.setEndTime(endTime);
            return this;
        }

        public Builder logs(String logs) {
            instance.setLogs(logs);
            return this;
        }

        public Builder resultData(Map<String, Object> resultData) {
            instance.setResultData(resultData);
            return this;
        }

        public Builder errorMessage(String errorMessage) {
            instance.setErrorMessage(errorMessage);
            return this;
        }

        public Builder userId(Long userId) {
            instance.setUserId(userId);
            return this;
        }

        public Builder createdAt(LocalDateTime createdAt) {
            instance.setCreatedAt(createdAt);
            return this;
        }

        public Builder updatedAt(LocalDateTime updatedAt) {
            instance.setUpdatedAt(updatedAt);
            return this;
        }

        public DevOpsCiTaskInstance build() {
            return instance;
        }
    }

    public static Builder builder() {
        return new Builder();
    }
}
