package com.example.springvueapp.repository;

import com.example.springvueapp.entity.DevOpsApplicationEntity;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * DevOps应用的响应式Repository接口
 */
@Repository
public interface DevOpsApplicationRepository extends ReactiveCrudRepository<DevOpsApplicationEntity, Long> {

    /**
     * 根据用户ID查找所有应用
     * @param userId 用户ID
     * @return 应用列表
     */
    Flux<DevOpsApplicationEntity> findByUserId(Long userId);

    /**
     * 根据项目ID查找所有应用
     * @param projectId 项目ID
     * @return 应用列表
     */
    Flux<DevOpsApplicationEntity> findByProjectId(Long projectId);

    /**
     * 根据用户ID和项目ID查找应用
     * @param userId 用户ID
     * @param projectId 项目ID
     * @return 应用列表
     */
    Flux<DevOpsApplicationEntity> findByUserIdAndProjectId(Long userId, Long projectId);

    /**
     * 根据用户ID和应用名称查找应用
     * @param userId 用户ID
     * @param name 应用名称
     * @return 应用实体
     */
    Mono<DevOpsApplicationEntity> findByUserIdAndName(Long userId, String name);

    /**
     * 根据项目ID和应用名称查找应用
     * @param projectId 项目ID
     * @param name 应用名称
     * @return 应用实体
     */
    Mono<DevOpsApplicationEntity> findByProjectIdAndName(Long projectId, String name);

    /**
     * 根据用户ID和状态查找应用
     * @param userId 用户ID
     * @param status 应用状态
     * @return 应用列表
     */
    Flux<DevOpsApplicationEntity> findByUserIdAndStatus(Long userId, String status);

    /**
     * 检查应用名称在项目范围内是否存在
     * @param projectId 项目ID
     * @param name 应用名称
     * @return 是否存在
     */
    Mono<Boolean> existsByProjectIdAndName(Long projectId, String name);

    /**
     * 根据用户ID和应用ID查找应用
     * @param userId 用户ID
     * @param id 应用ID
     * @return 应用实体
     */
    Mono<DevOpsApplicationEntity> findByUserIdAndId(Long userId, Long id);

    /**
     * 根据用户ID删除应用
     * @param userId 用户ID
     * @param id 应用ID
     * @return 删除的行数
     */
    @Query("DELETE FROM devops_applications WHERE user_id = :userId AND id = :id")
    Mono<Integer> deleteByUserIdAndId(Long userId, Long id);

    /**
     * 统计项目的应用数量
     * @param projectId 项目ID
     * @return 应用数量
     */
    Mono<Long> countByProjectId(Long projectId);

    /**
     * 统计用户的应用数量
     * @param userId 用户ID
     * @return 应用数量
     */
    Mono<Long> countByUserId(Long userId);

    /**
     * 根据名称模糊查询用户的应用
     * @param userId 用户ID
     * @param namePattern 名称模式
     * @return 应用列表
     */
    @Query("SELECT * FROM devops_applications WHERE user_id = :userId AND name LIKE :namePattern ORDER BY created_at DESC")
    Flux<DevOpsApplicationEntity> findByUserIdAndNameContaining(Long userId, String namePattern);

    /**
     * 根据项目ID和名称模糊查询应用
     * @param projectId 项目ID
     * @param namePattern 名称模式
     * @return 应用列表
     */
    @Query("SELECT * FROM devops_applications WHERE project_id = :projectId AND name LIKE :namePattern ORDER BY created_at DESC")
    Flux<DevOpsApplicationEntity> findByProjectIdAndNameContaining(Long projectId, String namePattern);
}
