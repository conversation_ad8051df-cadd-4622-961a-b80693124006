package com.example.springvueapp.repository;

import com.example.springvueapp.entity.DevOpsComponentEntity;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * DevOps组件的响应式Repository接口
 */
@Repository
public interface DevOpsComponentRepository extends ReactiveCrudRepository<DevOpsComponentEntity, Long> {

    /**
     * 根据用户ID查找所有组件
     * @param userId 用户ID
     * @return 组件列表
     */
    Flux<DevOpsComponentEntity> findByUserId(Long userId);

    /**
     * 根据应用ID查找所有组件
     * @param applicationId 应用ID
     * @return 组件列表
     */
    Flux<DevOpsComponentEntity> findByApplicationId(Long applicationId);

    /**
     * 根据用户ID和应用ID查找组件
     * @param userId 用户ID
     * @param applicationId 应用ID
     * @return 组件列表
     */
    Flux<DevOpsComponentEntity> findByUserIdAndApplicationId(Long userId, Long applicationId);

    /**
     * 根据用户ID和组件名称查找组件
     * @param userId 用户ID
     * @param name 组件名称
     * @return 组件实体
     */
    Mono<DevOpsComponentEntity> findByUserIdAndName(Long userId, String name);

    /**
     * 根据应用ID和组件名称查找组件
     * @param applicationId 应用ID
     * @param name 组件名称
     * @return 组件实体
     */
    Mono<DevOpsComponentEntity> findByApplicationIdAndName(Long applicationId, String name);

    /**
     * 根据用户ID和状态查找组件
     * @param userId 用户ID
     * @param status 组件状态
     * @return 组件列表
     */
    Flux<DevOpsComponentEntity> findByUserIdAndStatus(Long userId, String status);

    /**
     * 根据仓库类型查找组件
     * @param userId 用户ID
     * @param repositoryType 仓库类型
     * @return 组件列表
     */
    Flux<DevOpsComponentEntity> findByUserIdAndRepositoryType(Long userId, String repositoryType);

    /**
     * 检查组件名称在应用范围内是否存在
     * @param applicationId 应用ID
     * @param name 组件名称
     * @return 是否存在
     */
    Mono<Boolean> existsByApplicationIdAndName(Long applicationId, String name);

    /**
     * 根据用户ID和组件ID查找组件
     * @param userId 用户ID
     * @param id 组件ID
     * @return 组件实体
     */
    Mono<DevOpsComponentEntity> findByUserIdAndId(Long userId, Long id);

    /**
     * 根据用户ID删除组件
     * @param userId 用户ID
     * @param id 组件ID
     * @return 删除的行数
     */
    @Query("DELETE FROM devops_components WHERE user_id = :userId AND id = :id")
    Mono<Integer> deleteByUserIdAndId(Long userId, Long id);

    /**
     * 统计应用的组件数量
     * @param applicationId 应用ID
     * @return 组件数量
     */
    Mono<Long> countByApplicationId(Long applicationId);

    /**
     * 统计用户的组件数量
     * @param userId 用户ID
     * @return 组件数量
     */
    Mono<Long> countByUserId(Long userId);

    /**
     * 根据名称模糊查询用户的组件
     * @param userId 用户ID
     * @param namePattern 名称模式
     * @return 组件列表
     */
    @Query("SELECT * FROM devops_components WHERE user_id = :userId AND name LIKE :namePattern ORDER BY created_at DESC")
    Flux<DevOpsComponentEntity> findByUserIdAndNameContaining(Long userId, String namePattern);

    /**
     * 根据应用ID和名称模糊查询组件
     * @param applicationId 应用ID
     * @param namePattern 名称模式
     * @return 组件列表
     */
    @Query("SELECT * FROM devops_components WHERE application_id = :applicationId AND name LIKE :namePattern ORDER BY created_at DESC")
    Flux<DevOpsComponentEntity> findByApplicationIdAndNameContaining(Long applicationId, String namePattern);

    /**
     * 根据仓库URL查找组件
     * @param userId 用户ID
     * @param repositoryUrl 仓库URL
     * @return 组件实体
     */
    Mono<DevOpsComponentEntity> findByUserIdAndRepositoryUrl(Long userId, String repositoryUrl);

    /**
     * 检查仓库URL是否已被使用
     * @param repositoryUrl 仓库URL
     * @return 是否存在
     */
    Mono<Boolean> existsByRepositoryUrl(String repositoryUrl);
}
