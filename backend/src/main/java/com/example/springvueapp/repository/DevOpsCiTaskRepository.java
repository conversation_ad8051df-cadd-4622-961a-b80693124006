package com.example.springvueapp.repository;

import com.example.springvueapp.entity.DevOpsCiTaskEntity;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * DevOps CI任务的响应式Repository接口
 */
@Repository
public interface DevOpsCiTaskRepository extends ReactiveCrudRepository<DevOpsCiTaskEntity, Long> {

    /**
     * 根据用户ID查找所有CI任务
     * @param userId 用户ID
     * @return CI任务列表
     */
    Flux<DevOpsCiTaskEntity> findByUserId(Long userId);

    /**
     * 根据组件ID查找所有CI任务
     * @param componentId 组件ID
     * @return CI任务列表
     */
    Flux<DevOpsCiTaskEntity> findByComponentId(Long componentId);

    /**
     * 根据用户ID和组件ID查找CI任务
     * @param userId 用户ID
     * @param componentId 组件ID
     * @return CI任务列表
     */
    Flux<DevOpsCiTaskEntity> findByUserIdAndComponentId(Long userId, Long componentId);

    /**
     * 根据用户ID和任务名称查找CI任务
     * @param userId 用户ID
     * @param name 任务名称
     * @return CI任务实体
     */
    Mono<DevOpsCiTaskEntity> findByUserIdAndName(Long userId, String name);

    /**
     * 根据组件ID和任务名称查找CI任务
     * @param componentId 组件ID
     * @param name 任务名称
     * @return CI任务实体
     */
    Mono<DevOpsCiTaskEntity> findByComponentIdAndName(Long componentId, String name);

    /**
     * 根据用户ID和状态查找CI任务
     * @param userId 用户ID
     * @param status 任务状态
     * @return CI任务列表
     */
    Flux<DevOpsCiTaskEntity> findByUserIdAndStatus(Long userId, String status);

    /**
     * 根据用户ID和任务类型查找CI任务
     * @param userId 用户ID
     * @param taskType 任务类型
     * @return CI任务列表
     */
    Flux<DevOpsCiTaskEntity> findByUserIdAndTaskType(Long userId, String taskType);

    /**
     * 检查任务名称在组件范围内是否存在
     * @param componentId 组件ID
     * @param name 任务名称
     * @return 是否存在
     */
    Mono<Boolean> existsByComponentIdAndName(Long componentId, String name);

    /**
     * 根据用户ID和任务ID查找CI任务
     * @param userId 用户ID
     * @param id 任务ID
     * @return CI任务实体
     */
    Mono<DevOpsCiTaskEntity> findByUserIdAndId(Long userId, Long id);

    /**
     * 根据用户ID删除CI任务
     * @param userId 用户ID
     * @param id 任务ID
     * @return 删除的行数
     */
    @Query("DELETE FROM devops_ci_tasks WHERE user_id = :userId AND id = :id")
    Mono<Integer> deleteByUserIdAndId(Long userId, Long id);

    /**
     * 统计组件的CI任务数量
     * @param componentId 组件ID
     * @return CI任务数量
     */
    Mono<Long> countByComponentId(Long componentId);

    /**
     * 统计用户的CI任务数量
     * @param userId 用户ID
     * @return CI任务数量
     */
    Mono<Long> countByUserId(Long userId);

    /**
     * 统计指定类型的CI任务数量
     * @param userId 用户ID
     * @param taskType 任务类型
     * @return CI任务数量
     */
    Mono<Long> countByUserIdAndTaskType(Long userId, String taskType);

    /**
     * 根据名称模糊查询用户的CI任务
     * @param userId 用户ID
     * @param namePattern 名称模式
     * @return CI任务列表
     */
    @Query("SELECT * FROM devops_ci_tasks WHERE user_id = :userId AND name LIKE :namePattern ORDER BY created_at DESC")
    Flux<DevOpsCiTaskEntity> findByUserIdAndNameContaining(Long userId, String namePattern);

    /**
     * 根据组件ID和名称模糊查询CI任务
     * @param componentId 组件ID
     * @param namePattern 名称模式
     * @return CI任务列表
     */
    @Query("SELECT * FROM devops_ci_tasks WHERE component_id = :componentId AND name LIKE :namePattern ORDER BY created_at DESC")
    Flux<DevOpsCiTaskEntity> findByComponentIdAndNameContaining(Long componentId, String namePattern);

    /**
     * 获取所有任务类型
     * @param userId 用户ID
     * @return 任务类型列表
     */
    @Query("SELECT DISTINCT task_type FROM devops_ci_tasks WHERE user_id = :userId ORDER BY task_type")
    Flux<String> findDistinctTaskTypesByUserId(Long userId);

    /**
     * 根据版本查找CI任务
     * @param userId 用户ID
     * @param version 版本
     * @return CI任务列表
     */
    Flux<DevOpsCiTaskEntity> findByUserIdAndVersion(Long userId, String version);
}
