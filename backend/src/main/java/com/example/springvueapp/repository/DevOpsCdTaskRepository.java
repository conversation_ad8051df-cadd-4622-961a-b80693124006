package com.example.springvueapp.repository;

import com.example.springvueapp.entity.DevOpsCdTaskEntity;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * DevOps CD任务的响应式Repository接口
 */
@Repository
public interface DevOpsCdTaskRepository extends ReactiveCrudRepository<DevOpsCdTaskEntity, Long> {

    /**
     * 根据用户ID查找所有CD任务
     * @param userId 用户ID
     * @return CD任务列表
     */
    Flux<DevOpsCdTaskEntity> findByUserId(Long userId);

    /**
     * 根据应用ID查找所有CD任务
     * @param applicationId 应用ID
     * @return CD任务列表
     */
    Flux<DevOpsCdTaskEntity> findByApplicationId(Long applicationId);

    /**
     * 根据用户ID和应用ID查找CD任务
     * @param userId 用户ID
     * @param applicationId 应用ID
     * @return CD任务列表
     */
    Flux<DevOpsCdTaskEntity> findByUserIdAndApplicationId(Long userId, Long applicationId);

    /**
     * 根据用户ID和任务名称查找CD任务
     * @param userId 用户ID
     * @param name 任务名称
     * @return CD任务实体
     */
    Mono<DevOpsCdTaskEntity> findByUserIdAndName(Long userId, String name);

    /**
     * 根据应用ID和任务名称查找CD任务
     * @param applicationId 应用ID
     * @param name 任务名称
     * @return CD任务实体
     */
    Mono<DevOpsCdTaskEntity> findByApplicationIdAndName(Long applicationId, String name);

    /**
     * 根据用户ID和状态查找CD任务
     * @param userId 用户ID
     * @param status 任务状态
     * @return CD任务列表
     */
    Flux<DevOpsCdTaskEntity> findByUserIdAndStatus(Long userId, String status);

    /**
     * 检查任务名称在应用范围内是否存在
     * @param applicationId 应用ID
     * @param name 任务名称
     * @return 是否存在
     */
    Mono<Boolean> existsByApplicationIdAndName(Long applicationId, String name);

    /**
     * 根据用户ID和任务ID查找CD任务
     * @param userId 用户ID
     * @param id 任务ID
     * @return CD任务实体
     */
    Mono<DevOpsCdTaskEntity> findByUserIdAndId(Long userId, Long id);

    /**
     * 根据用户ID删除CD任务
     * @param userId 用户ID
     * @param id 任务ID
     * @return 删除的行数
     */
    @Query("DELETE FROM devops_cd_tasks WHERE user_id = :userId AND id = :id")
    Mono<Integer> deleteByUserIdAndId(Long userId, Long id);

    /**
     * 统计应用的CD任务数量
     * @param applicationId 应用ID
     * @return CD任务数量
     */
    Mono<Long> countByApplicationId(Long applicationId);

    /**
     * 统计用户的CD任务数量
     * @param userId 用户ID
     * @return CD任务数量
     */
    Mono<Long> countByUserId(Long userId);

    /**
     * 根据名称模糊查询用户的CD任务
     * @param userId 用户ID
     * @param namePattern 名称模式
     * @return CD任务列表
     */
    @Query("SELECT * FROM devops_cd_tasks WHERE user_id = :userId AND name LIKE :namePattern ORDER BY created_at DESC")
    Flux<DevOpsCdTaskEntity> findByUserIdAndNameContaining(Long userId, String namePattern);

    /**
     * 根据应用ID和名称模糊查询CD任务
     * @param applicationId 应用ID
     * @param namePattern 名称模式
     * @return CD任务列表
     */
    @Query("SELECT * FROM devops_cd_tasks WHERE application_id = :applicationId AND name LIKE :namePattern ORDER BY created_at DESC")
    Flux<DevOpsCdTaskEntity> findByApplicationIdAndNameContaining(Long applicationId, String namePattern);

    /**
     * 获取活跃的CD任务
     * @param userId 用户ID
     * @return 活跃的CD任务列表
     */
    @Query("SELECT * FROM devops_cd_tasks WHERE user_id = :userId AND status IN ('ACTIVE', 'RUNNING', 'DEPLOYING') ORDER BY updated_at DESC")
    Flux<DevOpsCdTaskEntity> findActiveCdTasks(Long userId);

    /**
     * 根据组件版本查找CD任务
     * @param userId 用户ID
     * @param componentName 组件名称
     * @param version 版本
     * @return CD任务列表
     */
    @Query("SELECT * FROM devops_cd_tasks WHERE user_id = :userId AND component_versions LIKE :versionPattern")
    Flux<DevOpsCdTaskEntity> findByComponentVersion(Long userId, String versionPattern);
}
