package com.example.springvueapp.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * DevOps部署模板的数据库实体类
 * 对应devops_deployment_templates表
 */
@Table("devops_deployment_templates")
public class DevOpsDeploymentTemplateEntity {

    @Id
    private Long id;

    private String name;

    private String description;

    private String templateType;

    private String templateContent; // 模板内容

    private String variables; // JSON格式的变量定义

    private Long userId;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    public DevOpsDeploymentTemplateEntity() {
    }

    public DevOpsDeploymentTemplateEntity(Long id, String name, String description, String templateType,
                                         String templateContent, String variables, Long userId,
                                         LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.templateType = templateType;
        this.templateContent = templateContent;
        this.variables = variables;
        this.userId = userId;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getTemplateType() {
        return templateType;
    }

    public void setTemplateType(String templateType) {
        this.templateType = templateType;
    }

    public String getTemplateContent() {
        return templateContent;
    }

    public void setTemplateContent(String templateContent) {
        this.templateContent = templateContent;
    }

    public String getVariables() {
        return variables;
    }

    public void setVariables(String variables) {
        this.variables = variables;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "DevOpsDeploymentTemplateEntity{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", templateType='" + templateType + '\'' +
                ", templateContent='" + templateContent + '\'' +
                ", variables='" + variables + '\'' +
                ", userId=" + userId +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }

    /**
     * DevOpsDeploymentTemplateEntity 的构建器类
     */
    public static class Builder {
        private DevOpsDeploymentTemplateEntity template = new DevOpsDeploymentTemplateEntity();

        public Builder id(Long id) {
            template.setId(id);
            return this;
        }

        public Builder name(String name) {
            template.setName(name);
            return this;
        }

        public Builder description(String description) {
            template.setDescription(description);
            return this;
        }

        public Builder templateType(String templateType) {
            template.setTemplateType(templateType);
            return this;
        }

        public Builder templateContent(String templateContent) {
            template.setTemplateContent(templateContent);
            return this;
        }

        public Builder variables(String variables) {
            template.setVariables(variables);
            return this;
        }

        public Builder userId(Long userId) {
            template.setUserId(userId);
            return this;
        }

        public Builder createdAt(LocalDateTime createdAt) {
            template.setCreatedAt(createdAt);
            return this;
        }

        public Builder updatedAt(LocalDateTime updatedAt) {
            template.setUpdatedAt(updatedAt);
            return this;
        }

        public DevOpsDeploymentTemplateEntity build() {
            return template;
        }
    }

    public static Builder builder() {
        return new Builder();
    }
}
