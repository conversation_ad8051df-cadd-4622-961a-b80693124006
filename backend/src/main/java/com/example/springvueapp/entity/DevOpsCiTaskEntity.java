package com.example.springvueapp.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * DevOps CI任务的数据库实体类
 * 对应devops_ci_tasks表
 */
@Table("devops_ci_tasks")
public class DevOpsCiTaskEntity {

    @Id
    private Long id;

    private String name;

    private String description;

    private Long componentId;

    private String version;

    private String taskType;

    private String status;

    private String configuration; // JSON配置信息

    private Long userId;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    public DevOpsCiTaskEntity() {
    }

    public DevOpsCiTaskEntity(Long id, String name, String description, Long componentId,
                             String version, String taskType, String status, String configuration,
                             Long userId, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.componentId = componentId;
        this.version = version;
        this.taskType = taskType;
        this.status = status;
        this.configuration = configuration;
        this.userId = userId;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getComponentId() {
        return componentId;
    }

    public void setComponentId(Long componentId) {
        this.componentId = componentId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getConfiguration() {
        return configuration;
    }

    public void setConfiguration(String configuration) {
        this.configuration = configuration;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "DevOpsCiTaskEntity{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", componentId=" + componentId +
                ", version='" + version + '\'' +
                ", taskType='" + taskType + '\'' +
                ", status='" + status + '\'' +
                ", configuration='" + configuration + '\'' +
                ", userId=" + userId +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }

    /**
     * DevOpsCiTaskEntity 的构建器类
     */
    public static class Builder {
        private DevOpsCiTaskEntity ciTask = new DevOpsCiTaskEntity();

        public Builder id(Long id) {
            ciTask.setId(id);
            return this;
        }

        public Builder name(String name) {
            ciTask.setName(name);
            return this;
        }

        public Builder description(String description) {
            ciTask.setDescription(description);
            return this;
        }

        public Builder componentId(Long componentId) {
            ciTask.setComponentId(componentId);
            return this;
        }

        public Builder version(String version) {
            ciTask.setVersion(version);
            return this;
        }

        public Builder taskType(String taskType) {
            ciTask.setTaskType(taskType);
            return this;
        }

        public Builder status(String status) {
            ciTask.setStatus(status);
            return this;
        }

        public Builder configuration(String configuration) {
            ciTask.setConfiguration(configuration);
            return this;
        }

        public Builder userId(Long userId) {
            ciTask.setUserId(userId);
            return this;
        }

        public Builder createdAt(LocalDateTime createdAt) {
            ciTask.setCreatedAt(createdAt);
            return this;
        }

        public Builder updatedAt(LocalDateTime updatedAt) {
            ciTask.setUpdatedAt(updatedAt);
            return this;
        }

        public DevOpsCiTaskEntity build() {
            return ciTask;
        }
    }

    public static Builder builder() {
        return new Builder();
    }
}
