package com.example.springvueapp.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * DevOps CI任务实例的数据库实体类
 * 对应devops_ci_task_instances表
 */
@Table("devops_ci_task_instances")
public class DevOpsCiTaskInstanceEntity {

    @Id
    private Long id;

    private Long ciTaskId;

    private String instanceId;

    private String status;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private String logs;

    private String resultData; // JSON格式的执行结果数据

    private String errorMessage;

    private Long userId;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    public DevOpsCiTaskInstanceEntity() {
    }

    public DevOpsCiTaskInstanceEntity(Long id, Long ciTaskId, String instanceId, String status,
                                     LocalDateTime startTime, LocalDateTime endTime, String logs,
                                     String resultData, String errorMessage, Long userId,
                                     LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.ciTaskId = ciTaskId;
        this.instanceId = instanceId;
        this.status = status;
        this.startTime = startTime;
        this.endTime = endTime;
        this.logs = logs;
        this.resultData = resultData;
        this.errorMessage = errorMessage;
        this.userId = userId;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCiTaskId() {
        return ciTaskId;
    }

    public void setCiTaskId(Long ciTaskId) {
        this.ciTaskId = ciTaskId;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getLogs() {
        return logs;
    }

    public void setLogs(String logs) {
        this.logs = logs;
    }

    public String getResultData() {
        return resultData;
    }

    public void setResultData(String resultData) {
        this.resultData = resultData;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "DevOpsCiTaskInstanceEntity{" +
                "id=" + id +
                ", ciTaskId=" + ciTaskId +
                ", instanceId='" + instanceId + '\'' +
                ", status='" + status + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", logs='" + logs + '\'' +
                ", resultData='" + resultData + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", userId=" + userId +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }

    /**
     * DevOpsCiTaskInstanceEntity 的构建器类
     */
    public static class Builder {
        private DevOpsCiTaskInstanceEntity instance = new DevOpsCiTaskInstanceEntity();

        public Builder id(Long id) {
            instance.setId(id);
            return this;
        }

        public Builder ciTaskId(Long ciTaskId) {
            instance.setCiTaskId(ciTaskId);
            return this;
        }

        public Builder instanceId(String instanceId) {
            instance.setInstanceId(instanceId);
            return this;
        }

        public Builder status(String status) {
            instance.setStatus(status);
            return this;
        }

        public Builder startTime(LocalDateTime startTime) {
            instance.setStartTime(startTime);
            return this;
        }

        public Builder endTime(LocalDateTime endTime) {
            instance.setEndTime(endTime);
            return this;
        }

        public Builder logs(String logs) {
            instance.setLogs(logs);
            return this;
        }

        public Builder resultData(String resultData) {
            instance.setResultData(resultData);
            return this;
        }

        public Builder errorMessage(String errorMessage) {
            instance.setErrorMessage(errorMessage);
            return this;
        }

        public Builder userId(Long userId) {
            instance.setUserId(userId);
            return this;
        }

        public Builder createdAt(LocalDateTime createdAt) {
            instance.setCreatedAt(createdAt);
            return this;
        }

        public Builder updatedAt(LocalDateTime updatedAt) {
            instance.setUpdatedAt(updatedAt);
            return this;
        }

        public DevOpsCiTaskInstanceEntity build() {
            return instance;
        }
    }

    public static Builder builder() {
        return new Builder();
    }
}
