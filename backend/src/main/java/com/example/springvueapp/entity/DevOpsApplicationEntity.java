package com.example.springvueapp.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * DevOps应用的数据库实体类
 * 对应devops_applications表
 */
@Table("devops_applications")
public class DevOpsApplicationEntity {

    @Id
    private Long id;

    private String name;

    private String description;

    private Long projectId;

    private String status;

    private Long userId;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    public DevOpsApplicationEntity() {
    }

    public DevOpsApplicationEntity(Long id, String name, String description, Long projectId,
                                  String status, Long userId, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.projectId = projectId;
        this.status = status;
        this.userId = userId;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "DevOpsApplicationEntity{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", projectId=" + projectId +
                ", status='" + status + '\'' +
                ", userId=" + userId +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }

    /**
     * DevOpsApplicationEntity 的构建器类
     */
    public static class Builder {
        private DevOpsApplicationEntity application = new DevOpsApplicationEntity();

        public Builder id(Long id) {
            application.setId(id);
            return this;
        }

        public Builder name(String name) {
            application.setName(name);
            return this;
        }

        public Builder description(String description) {
            application.setDescription(description);
            return this;
        }

        public Builder projectId(Long projectId) {
            application.setProjectId(projectId);
            return this;
        }

        public Builder status(String status) {
            application.setStatus(status);
            return this;
        }

        public Builder userId(Long userId) {
            application.setUserId(userId);
            return this;
        }

        public Builder createdAt(LocalDateTime createdAt) {
            application.setCreatedAt(createdAt);
            return this;
        }

        public Builder updatedAt(LocalDateTime updatedAt) {
            application.setUpdatedAt(updatedAt);
            return this;
        }

        public DevOpsApplicationEntity build() {
            return application;
        }
    }

    public static Builder builder() {
        return new Builder();
    }
}
