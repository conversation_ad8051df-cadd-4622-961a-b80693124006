package com.example.springvueapp.service;

import com.example.springvueapp.entity.DevOpsProjectEntity;
import com.example.springvueapp.mapper.DevOpsProjectMapper;
import com.example.springvueapp.model.DevOpsProject;
import com.example.springvueapp.repository.DevOpsProjectRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * DevOps项目管理服务
 */
@Service
public class DevOpsProjectService {

    private final DevOpsProjectRepository projectRepository;
    private final DevOpsProjectMapper projectMapper;

    @Autowired
    public DevOpsProjectService(DevOpsProjectRepository projectRepository, 
                               DevOpsProjectMapper projectMapper) {
        this.projectRepository = projectRepository;
        this.projectMapper = projectMapper;
    }

    /**
     * 创建新项目
     * @param project 项目信息
     * @param userId 用户ID
     * @return 创建的项目
     */
    public Mono<DevOpsProject> createProject(DevOpsProject project, Long userId) {
        return projectRepository.existsByUserIdAndName(userId, project.getName())
                .flatMap(exists -> {
                    if (exists) {
                        return Mono.error(new IllegalArgumentException("项目名称已存在"));
                    }
                    DevOpsProjectEntity entity = projectMapper.toNewEntity(project, userId);
                    return projectRepository.save(entity)
                            .map(projectMapper::toDto);
                });
    }

    /**
     * 根据ID获取项目
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 项目信息
     */
    public Mono<DevOpsProject> getProjectById(Long projectId, Long userId) {
        return projectRepository.findByUserIdAndId(userId, projectId)
                .map(projectMapper::toDto)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("项目不存在或无权限访问")));
    }

    /**
     * 获取用户的所有项目
     * @param userId 用户ID
     * @return 项目列表
     */
    public Flux<DevOpsProject> getAllProjects(Long userId) {
        return projectRepository.findByUserId(userId)
                .map(projectMapper::toDto);
    }

    /**
     * 根据状态获取项目
     * @param userId 用户ID
     * @param status 项目状态
     * @return 项目列表
     */
    public Flux<DevOpsProject> getProjectsByStatus(Long userId, String status) {
        return projectRepository.findByUserIdAndStatus(userId, status)
                .map(projectMapper::toDto);
    }

    /**
     * 更新项目
     * @param projectId 项目ID
     * @param project 更新的项目信息
     * @param userId 用户ID
     * @return 更新后的项目
     */
    public Mono<DevOpsProject> updateProject(Long projectId, DevOpsProject project, Long userId) {
        return projectRepository.findByUserIdAndId(userId, projectId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("项目不存在或无权限访问")))
                .flatMap(existingEntity -> {
                    // 如果名称发生变化，检查新名称是否已存在
                    if (!existingEntity.getName().equals(project.getName())) {
                        return projectRepository.existsByUserIdAndName(userId, project.getName())
                                .flatMap(exists -> {
                                    if (exists) {
                                        return Mono.error(new IllegalArgumentException("项目名称已存在"));
                                    }
                                    DevOpsProjectEntity updatedEntity = projectMapper.toUpdateEntity(project, existingEntity);
                                    return projectRepository.save(updatedEntity);
                                });
                    } else {
                        DevOpsProjectEntity updatedEntity = projectMapper.toUpdateEntity(project, existingEntity);
                        return projectRepository.save(updatedEntity);
                    }
                })
                .map(projectMapper::toDto);
    }

    /**
     * 删除项目
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 删除结果
     */
    public Mono<Boolean> deleteProject(Long projectId, Long userId) {
        return projectRepository.findByUserIdAndId(userId, projectId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("项目不存在或无权限访问")))
                .flatMap(entity -> projectRepository.deleteByUserIdAndId(userId, projectId))
                .map(deletedCount -> deletedCount > 0);
    }

    /**
     * 搜索项目
     * @param userId 用户ID
     * @param keyword 搜索关键词
     * @return 项目列表
     */
    public Flux<DevOpsProject> searchProjects(Long userId, String keyword) {
        String pattern = "%" + keyword + "%";
        return projectRepository.findByUserIdAndNameContaining(userId, pattern)
                .map(projectMapper::toDto);
    }

    /**
     * 统计用户的项目数量
     * @param userId 用户ID
     * @return 项目数量
     */
    public Mono<Long> countProjects(Long userId) {
        return projectRepository.countByUserId(userId);
    }
}
