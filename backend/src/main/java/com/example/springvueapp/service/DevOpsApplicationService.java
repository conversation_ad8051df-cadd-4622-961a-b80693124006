package com.example.springvueapp.service;

import com.example.springvueapp.entity.DevOpsApplicationEntity;
import com.example.springvueapp.mapper.DevOpsApplicationMapper;
import com.example.springvueapp.model.DevOpsApplication;
import com.example.springvueapp.repository.DevOpsApplicationRepository;
import com.example.springvueapp.repository.DevOpsProjectRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * DevOps应用管理服务
 */
@Service
public class DevOpsApplicationService {

    private final DevOpsApplicationRepository applicationRepository;
    private final DevOpsProjectRepository projectRepository;
    private final DevOpsApplicationMapper applicationMapper;

    @Autowired
    public DevOpsApplicationService(DevOpsApplicationRepository applicationRepository,
                                   DevOpsProjectRepository projectRepository,
                                   DevOpsApplicationMapper applicationMapper) {
        this.applicationRepository = applicationRepository;
        this.projectRepository = projectRepository;
        this.applicationMapper = applicationMapper;
    }

    /**
     * 创建新应用
     * @param application 应用信息
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 创建的应用
     */
    public Mono<DevOpsApplication> createApplication(DevOpsApplication application, Long projectId, Long userId) {
        // 首先验证项目是否存在且用户有权限
        return projectRepository.findByUserIdAndId(userId, projectId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("项目不存在或无权限访问")))
                .flatMap(project -> 
                    applicationRepository.existsByProjectIdAndName(projectId, application.getName())
                            .flatMap(exists -> {
                                if (exists) {
                                    return Mono.error(new IllegalArgumentException("应用名称在该项目中已存在"));
                                }
                                DevOpsApplicationEntity entity = applicationMapper.toNewEntity(application, projectId, userId);
                                return applicationRepository.save(entity)
                                        .map(applicationMapper::toDto);
                            })
                );
    }

    /**
     * 根据ID获取应用
     * @param applicationId 应用ID
     * @param userId 用户ID
     * @return 应用信息
     */
    public Mono<DevOpsApplication> getApplicationById(Long applicationId, Long userId) {
        return applicationRepository.findByUserIdAndId(userId, applicationId)
                .map(applicationMapper::toDto)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("应用不存在或无权限访问")));
    }

    /**
     * 获取用户的所有应用
     * @param userId 用户ID
     * @return 应用列表
     */
    public Flux<DevOpsApplication> getAllApplications(Long userId) {
        return applicationRepository.findByUserId(userId)
                .map(applicationMapper::toDto);
    }

    /**
     * 获取项目下的所有应用
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 应用列表
     */
    public Flux<DevOpsApplication> getApplicationsByProject(Long projectId, Long userId) {
        // 首先验证项目是否存在且用户有权限
        return projectRepository.findByUserIdAndId(userId, projectId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("项目不存在或无权限访问")))
                .flatMapMany(project -> 
                    applicationRepository.findByUserIdAndProjectId(userId, projectId)
                            .map(applicationMapper::toDto)
                );
    }

    /**
     * 根据状态获取应用
     * @param userId 用户ID
     * @param status 应用状态
     * @return 应用列表
     */
    public Flux<DevOpsApplication> getApplicationsByStatus(Long userId, String status) {
        return applicationRepository.findByUserIdAndStatus(userId, status)
                .map(applicationMapper::toDto);
    }

    /**
     * 更新应用
     * @param applicationId 应用ID
     * @param application 更新的应用信息
     * @param userId 用户ID
     * @return 更新后的应用
     */
    public Mono<DevOpsApplication> updateApplication(Long applicationId, DevOpsApplication application, Long userId) {
        return applicationRepository.findByUserIdAndId(userId, applicationId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("应用不存在或无权限访问")))
                .flatMap(existingEntity -> {
                    // 如果名称发生变化，检查新名称在项目中是否已存在
                    if (!existingEntity.getName().equals(application.getName())) {
                        return applicationRepository.existsByProjectIdAndName(existingEntity.getProjectId(), application.getName())
                                .flatMap(exists -> {
                                    if (exists) {
                                        return Mono.error(new IllegalArgumentException("应用名称在该项目中已存在"));
                                    }
                                    DevOpsApplicationEntity updatedEntity = applicationMapper.toUpdateEntity(application, existingEntity);
                                    return applicationRepository.save(updatedEntity);
                                });
                    } else {
                        DevOpsApplicationEntity updatedEntity = applicationMapper.toUpdateEntity(application, existingEntity);
                        return applicationRepository.save(updatedEntity);
                    }
                })
                .map(applicationMapper::toDto);
    }

    /**
     * 删除应用
     * @param applicationId 应用ID
     * @param userId 用户ID
     * @return 删除结果
     */
    public Mono<Boolean> deleteApplication(Long applicationId, Long userId) {
        return applicationRepository.findByUserIdAndId(userId, applicationId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("应用不存在或无权限访问")))
                .flatMap(entity -> applicationRepository.deleteByUserIdAndId(userId, applicationId))
                .map(deletedCount -> deletedCount > 0);
    }

    /**
     * 搜索应用
     * @param userId 用户ID
     * @param keyword 搜索关键词
     * @return 应用列表
     */
    public Flux<DevOpsApplication> searchApplications(Long userId, String keyword) {
        String pattern = "%" + keyword + "%";
        return applicationRepository.findByUserIdAndNameContaining(userId, pattern)
                .map(applicationMapper::toDto);
    }

    /**
     * 在项目中搜索应用
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param keyword 搜索关键词
     * @return 应用列表
     */
    public Flux<DevOpsApplication> searchApplicationsInProject(Long projectId, Long userId, String keyword) {
        // 首先验证项目是否存在且用户有权限
        return projectRepository.findByUserIdAndId(userId, projectId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("项目不存在或无权限访问")))
                .flatMapMany(project -> {
                    String pattern = "%" + keyword + "%";
                    return applicationRepository.findByProjectIdAndNameContaining(projectId, pattern)
                            .map(applicationMapper::toDto);
                });
    }

    /**
     * 统计项目的应用数量
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 应用数量
     */
    public Mono<Long> countApplicationsByProject(Long projectId, Long userId) {
        // 首先验证项目是否存在且用户有权限
        return projectRepository.findByUserIdAndId(userId, projectId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("项目不存在或无权限访问")))
                .flatMap(project -> applicationRepository.countByProjectId(projectId));
    }

    /**
     * 统计用户的应用数量
     * @param userId 用户ID
     * @return 应用数量
     */
    public Mono<Long> countApplications(Long userId) {
        return applicationRepository.countByUserId(userId);
    }
}
