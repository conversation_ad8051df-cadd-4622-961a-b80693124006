package com.example.springvueapp.service;

import com.example.springvueapp.entity.DevOpsCiTaskEntity;
import com.example.springvueapp.entity.DevOpsCiTaskInstanceEntity;
import com.example.springvueapp.mapper.DevOpsCiTaskInstanceMapper;
import com.example.springvueapp.mapper.DevOpsCiTaskMapper;
import com.example.springvueapp.model.DevOpsCiTask;
import com.example.springvueapp.model.DevOpsCiTaskInstance;
import com.example.springvueapp.repository.DevOpsCiTaskInstanceRepository;
import com.example.springvueapp.repository.DevOpsCiTaskRepository;
import com.example.springvueapp.repository.DevOpsComponentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Tekton CI任务服务实现
 * 实现CiTaskService抽象接口，支持Task和Pipeline模板管理、YAML配置生成和任务监控功能
 */
@Service
public class TektonCiTaskService implements CiTaskService {

    private final DevOpsCiTaskRepository ciTaskRepository;
    private final DevOpsCiTaskInstanceRepository ciTaskInstanceRepository;
    private final DevOpsComponentRepository componentRepository;
    private final DevOpsCiTaskMapper ciTaskMapper;
    private final DevOpsCiTaskInstanceMapper ciTaskInstanceMapper;

    @Autowired
    public TektonCiTaskService(DevOpsCiTaskRepository ciTaskRepository,
                              DevOpsCiTaskInstanceRepository ciTaskInstanceRepository,
                              DevOpsComponentRepository componentRepository,
                              DevOpsCiTaskMapper ciTaskMapper,
                              DevOpsCiTaskInstanceMapper ciTaskInstanceMapper) {
        this.ciTaskRepository = ciTaskRepository;
        this.ciTaskInstanceRepository = ciTaskInstanceRepository;
        this.componentRepository = componentRepository;
        this.ciTaskMapper = ciTaskMapper;
        this.ciTaskInstanceMapper = ciTaskInstanceMapper;
    }

    @Override
    public Mono<DevOpsCiTask> createCiTask(DevOpsCiTask ciTask, Long componentId, Long userId) {
        // 首先验证组件是否存在且用户有权限
        return componentRepository.findByUserIdAndId(userId, componentId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("组件不存在或无权限访问")))
                .flatMap(component -> 
                    ciTaskRepository.existsByComponentIdAndName(componentId, ciTask.getName())
                            .flatMap(exists -> {
                                if (exists) {
                                    return Mono.error(new IllegalArgumentException("CI任务名称在该组件中已存在"));
                                }
                                
                                // 验证配置
                                return validateConfiguration(ciTask.getConfiguration())
                                        .flatMap(valid -> {
                                            if (!valid) {
                                                return Mono.error(new IllegalArgumentException("CI任务配置无效"));
                                            }
                                            
                                            DevOpsCiTaskEntity entity = ciTaskMapper.toNewEntity(ciTask, componentId, userId);
                                            return ciTaskRepository.save(entity)
                                                    .map(ciTaskMapper::toDto);
                                        });
                            })
                );
    }

    @Override
    public Mono<DevOpsCiTask> updateCiTask(Long taskId, DevOpsCiTask ciTask, Long userId) {
        return ciTaskRepository.findByUserIdAndId(userId, taskId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务不存在或无权限访问")))
                .flatMap(existingEntity -> {
                    // 如果名称发生变化，检查新名称是否已存在
                    if (!existingEntity.getName().equals(ciTask.getName())) {
                        return ciTaskRepository.existsByComponentIdAndName(existingEntity.getComponentId(), ciTask.getName())
                                .flatMap(exists -> {
                                    if (exists) {
                                        return Mono.error(new IllegalArgumentException("CI任务名称在该组件中已存在"));
                                    }
                                    return updateTaskEntity(ciTask, existingEntity);
                                });
                    } else {
                        return updateTaskEntity(ciTask, existingEntity);
                    }
                });
    }

    private Mono<DevOpsCiTask> updateTaskEntity(DevOpsCiTask ciTask, DevOpsCiTaskEntity existingEntity) {
        // 验证配置
        return validateConfiguration(ciTask.getConfiguration())
                .flatMap(valid -> {
                    if (!valid) {
                        return Mono.error(new IllegalArgumentException("CI任务配置无效"));
                    }
                    
                    DevOpsCiTaskEntity updatedEntity = ciTaskMapper.toUpdateEntity(ciTask, existingEntity);
                    return ciTaskRepository.save(updatedEntity)
                            .map(ciTaskMapper::toDto);
                });
    }

    @Override
    public Mono<Boolean> deleteCiTask(Long taskId, Long userId) {
        return ciTaskRepository.findByUserIdAndId(userId, taskId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务不存在或无权限访问")))
                .flatMap(entity -> ciTaskRepository.deleteByUserIdAndId(userId, taskId))
                .map(deletedCount -> deletedCount > 0);
    }

    @Override
    public Mono<DevOpsCiTask> getCiTaskById(Long taskId, Long userId) {
        return ciTaskRepository.findByUserIdAndId(userId, taskId)
                .map(ciTaskMapper::toDto)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务不存在或无权限访问")));
    }

    @Override
    public Flux<DevOpsCiTask> getCiTasksByComponent(Long componentId, Long userId) {
        // 首先验证组件是否存在且用户有权限
        return componentRepository.findByUserIdAndId(userId, componentId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("组件不存在或无权限访问")))
                .flatMapMany(component -> 
                    ciTaskRepository.findByUserIdAndComponentId(userId, componentId)
                            .map(ciTaskMapper::toDto)
                );
    }

    @Override
    public Flux<DevOpsCiTask> getAllCiTasks(Long userId) {
        return ciTaskRepository.findByUserId(userId)
                .map(ciTaskMapper::toDto);
    }

    @Override
    public Mono<DevOpsCiTaskInstance> startCiTask(Long taskId, Long userId, Map<String, Object> parameters) {
        return ciTaskRepository.findByUserIdAndId(userId, taskId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务不存在或无权限访问")))
                .flatMap(ciTaskEntity -> {
                    // 生成唯一的实例ID
                    String instanceId = generateInstanceId(ciTaskEntity.getName());
                    
                    // 创建任务实例
                    DevOpsCiTaskInstance instance = DevOpsCiTaskInstance.builder()
                            .ciTaskId(taskId)
                            .instanceId(instanceId)
                            .status("PENDING")
                            .startTime(LocalDateTime.now())
                            .resultData(parameters != null ? parameters : new HashMap<>())
                            .build();
                    
                    DevOpsCiTaskInstanceEntity instanceEntity = ciTaskInstanceMapper.toNewEntity(instance, taskId, userId);
                    
                    return ciTaskInstanceRepository.save(instanceEntity)
                            .map(ciTaskInstanceMapper::toDto)
                            .flatMap(savedInstance -> {
                                // 这里应该调用Tekton API启动任务
                                // 暂时模拟启动过程
                                return simulateTektonTaskStart(savedInstance);
                            });
                });
    }

    @Override
    public Mono<Boolean> stopCiTaskInstance(String instanceId, Long userId) {
        return ciTaskInstanceRepository.findByUserIdAndInstanceId(userId, instanceId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务实例不存在或无权限访问")))
                .flatMap(instanceEntity -> {
                    // 这里应该调用Tekton API停止任务
                    // 暂时模拟停止过程
                    instanceEntity.setStatus("STOPPED");
                    instanceEntity.setEndTime(LocalDateTime.now());
                    instanceEntity.setUpdatedAt(LocalDateTime.now());
                    
                    return ciTaskInstanceRepository.save(instanceEntity)
                            .map(saved -> true);
                });
    }

    @Override
    public Mono<Boolean> cancelCiTaskInstance(String instanceId, Long userId) {
        return ciTaskInstanceRepository.findByUserIdAndInstanceId(userId, instanceId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务实例不存在或无权限访问")))
                .flatMap(instanceEntity -> {
                    // 这里应该调用Tekton API取消任务
                    // 暂时模拟取消过程
                    instanceEntity.setStatus("CANCELLED");
                    instanceEntity.setEndTime(LocalDateTime.now());
                    instanceEntity.setUpdatedAt(LocalDateTime.now());
                    
                    return ciTaskInstanceRepository.save(instanceEntity)
                            .map(saved -> true);
                });
    }

    @Override
    public Mono<DevOpsCiTaskInstance> getCiTaskInstanceStatus(String instanceId, Long userId) {
        return ciTaskInstanceRepository.findByUserIdAndInstanceId(userId, instanceId)
                .map(ciTaskInstanceMapper::toDto)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务实例不存在或无权限访问")));
    }

    @Override
    public Flux<DevOpsCiTaskInstance> getCiTaskInstances(Long taskId, Long userId) {
        return ciTaskRepository.findByUserIdAndId(userId, taskId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务不存在或无权限访问")))
                .flatMapMany(ciTaskEntity -> 
                    ciTaskInstanceRepository.findByUserIdAndCiTaskId(userId, taskId)
                            .map(ciTaskInstanceMapper::toDto)
                );
    }

    @Override
    public Mono<String> getCiTaskInstanceLogs(String instanceId, Long userId) {
        return ciTaskInstanceRepository.findByUserIdAndInstanceId(userId, instanceId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务实例不存在或无权限访问")))
                .map(instanceEntity -> instanceEntity.getLogs() != null ? instanceEntity.getLogs() : "暂无日志");
    }

    @Override
    public Mono<Integer> cleanupCompletedInstances(Long taskId, Long userId, int keepCount) {
        return ciTaskRepository.findByUserIdAndId(userId, taskId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务不存在或无权限访问")))
                .flatMap(ciTaskEntity -> 
                    ciTaskInstanceRepository.deleteOldCompletedInstances(taskId, keepCount)
                );
    }

    @Override
    public Mono<Boolean> validateConfiguration(Map<String, Object> configuration) {
        // 实现Tekton配置验证逻辑
        if (configuration == null) {
            return Mono.just(true); // 允许空配置
        }
        
        // 基本验证：检查必要的字段
        // 这里可以添加更复杂的Tekton YAML验证逻辑
        return Mono.just(true);
    }

    @Override
    public Mono<Map<String, Object>> getTaskTemplate(String taskType) {
        // 返回Tekton任务模板
        Map<String, Object> template = new HashMap<>();
        
        switch (taskType.toLowerCase()) {
            case "build":
                template = createBuildTemplate();
                break;
            case "test":
                template = createTestTemplate();
                break;
            case "deploy":
                template = createDeployTemplate();
                break;
            default:
                template = createDefaultTemplate();
        }
        
        return Mono.just(template);
    }

    @Override
    public Flux<String> getSupportedTaskTypes() {
        return Flux.just("build", "test", "deploy", "custom");
    }

    @Override
    public Mono<Boolean> checkConnection() {
        // 这里应该检查与Tekton集群的连接
        // 暂时返回true
        return Mono.just(true);
    }

    @Override
    public Mono<Map<String, Object>> getPlatformInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("platform", "Tekton");
        info.put("version", "v0.50.0");
        info.put("status", "connected");
        return Mono.just(info);
    }

    /**
     * 生成实例ID
     */
    private String generateInstanceId(String taskName) {
        return taskName.toLowerCase().replaceAll("[^a-z0-9-]", "-") + "-" + 
               UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 模拟Tekton任务启动
     */
    private Mono<DevOpsCiTaskInstance> simulateTektonTaskStart(DevOpsCiTaskInstance instance) {
        // 这里应该调用实际的Tekton API
        // 暂时模拟启动过程
        instance.setStatus("RUNNING");
        return Mono.just(instance);
    }

    /**
     * 创建构建模板
     */
    private Map<String, Object> createBuildTemplate() {
        Map<String, Object> template = new HashMap<>();
        template.put("apiVersion", "tekton.dev/v1beta1");
        template.put("kind", "Task");
        
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("name", "build-task");
        template.put("metadata", metadata);
        
        Map<String, Object> spec = new HashMap<>();
        spec.put("description", "构建任务模板");
        template.put("spec", spec);
        
        return template;
    }

    /**
     * 创建测试模板
     */
    private Map<String, Object> createTestTemplate() {
        Map<String, Object> template = new HashMap<>();
        template.put("apiVersion", "tekton.dev/v1beta1");
        template.put("kind", "Task");
        
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("name", "test-task");
        template.put("metadata", metadata);
        
        Map<String, Object> spec = new HashMap<>();
        spec.put("description", "测试任务模板");
        template.put("spec", spec);
        
        return template;
    }

    /**
     * 创建部署模板
     */
    private Map<String, Object> createDeployTemplate() {
        Map<String, Object> template = new HashMap<>();
        template.put("apiVersion", "tekton.dev/v1beta1");
        template.put("kind", "Task");
        
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("name", "deploy-task");
        template.put("metadata", metadata);
        
        Map<String, Object> spec = new HashMap<>();
        spec.put("description", "部署任务模板");
        template.put("spec", spec);
        
        return template;
    }

    /**
     * 创建默认模板
     */
    private Map<String, Object> createDefaultTemplate() {
        Map<String, Object> template = new HashMap<>();
        template.put("apiVersion", "tekton.dev/v1beta1");
        template.put("kind", "Task");
        
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("name", "default-task");
        template.put("metadata", metadata);
        
        Map<String, Object> spec = new HashMap<>();
        spec.put("description", "默认任务模板");
        template.put("spec", spec);
        
        return template;
    }
}
