package com.example.springvueapp.service;

import com.example.springvueapp.entity.DevOpsResourceEntity;
import com.example.springvueapp.mapper.DevOpsResourceMapper;
import com.example.springvueapp.model.DevOpsResource;
import com.example.springvueapp.repository.DevOpsComponentRepository;
import com.example.springvueapp.repository.DevOpsResourceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * DevOps资源管理服务
 */
@Service
public class DevOpsResourceService {

    private final DevOpsResourceRepository resourceRepository;
    private final DevOpsComponentRepository componentRepository;
    private final DevOpsResourceMapper resourceMapper;

    @Autowired
    public DevOpsResourceService(DevOpsResourceRepository resourceRepository,
                                DevOpsComponentRepository componentRepository,
                                DevOpsResourceMapper resourceMapper) {
        this.resourceRepository = resourceRepository;
        this.componentRepository = componentRepository;
        this.resourceMapper = resourceMapper;
    }

    /**
     * 创建新资源
     * @param resource 资源信息
     * @param componentId 组件ID
     * @param userId 用户ID
     * @return 创建的资源
     */
    public Mono<DevOpsResource> createResource(DevOpsResource resource, Long componentId, Long userId) {
        // 首先验证组件是否存在且用户有权限
        return componentRepository.findByUserIdAndId(userId, componentId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("组件不存在或无权限访问")))
                .flatMap(component -> 
                    resourceRepository.existsByComponentIdAndName(componentId, resource.getName())
                            .flatMap(exists -> {
                                if (exists) {
                                    return Mono.error(new IllegalArgumentException("资源名称在该组件中已存在"));
                                }
                                // 检查路径是否已被使用（如果提供了路径）
                                if (resource.getPath() != null && !resource.getPath().trim().isEmpty()) {
                                    return resourceRepository.existsByComponentIdAndPath(componentId, resource.getPath())
                                            .flatMap(pathExists -> {
                                                if (pathExists) {
                                                    return Mono.error(new IllegalArgumentException("资源路径在该组件中已被使用"));
                                                }
                                                DevOpsResourceEntity entity = resourceMapper.toNewEntity(resource, componentId, userId);
                                                return resourceRepository.save(entity)
                                                        .map(resourceMapper::toDto);
                                            });
                                } else {
                                    DevOpsResourceEntity entity = resourceMapper.toNewEntity(resource, componentId, userId);
                                    return resourceRepository.save(entity)
                                            .map(resourceMapper::toDto);
                                }
                            })
                );
    }

    /**
     * 根据ID获取资源
     * @param resourceId 资源ID
     * @param userId 用户ID
     * @return 资源信息
     */
    public Mono<DevOpsResource> getResourceById(Long resourceId, Long userId) {
        return resourceRepository.findByUserIdAndId(userId, resourceId)
                .map(resourceMapper::toDto)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("资源不存在或无权限访问")));
    }

    /**
     * 获取用户的所有资源
     * @param userId 用户ID
     * @return 资源列表
     */
    public Flux<DevOpsResource> getAllResources(Long userId) {
        return resourceRepository.findByUserId(userId)
                .map(resourceMapper::toDto);
    }

    /**
     * 获取组件下的所有资源
     * @param componentId 组件ID
     * @param userId 用户ID
     * @return 资源列表
     */
    public Flux<DevOpsResource> getResourcesByComponent(Long componentId, Long userId) {
        // 首先验证组件是否存在且用户有权限
        return componentRepository.findByUserIdAndId(userId, componentId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("组件不存在或无权限访问")))
                .flatMapMany(component -> 
                    resourceRepository.findByUserIdAndComponentId(userId, componentId)
                            .map(resourceMapper::toDto)
                );
    }

    /**
     * 根据状态获取资源
     * @param userId 用户ID
     * @param status 资源状态
     * @return 资源列表
     */
    public Flux<DevOpsResource> getResourcesByStatus(Long userId, String status) {
        return resourceRepository.findByUserIdAndStatus(userId, status)
                .map(resourceMapper::toDto);
    }

    /**
     * 根据类型获取资源
     * @param userId 用户ID
     * @param type 资源类型
     * @return 资源列表
     */
    public Flux<DevOpsResource> getResourcesByType(Long userId, String type) {
        return resourceRepository.findByUserIdAndType(userId, type)
                .map(resourceMapper::toDto);
    }

    /**
     * 根据组件ID和类型获取资源
     * @param componentId 组件ID
     * @param userId 用户ID
     * @param type 资源类型
     * @return 资源列表
     */
    public Flux<DevOpsResource> getResourcesByComponentAndType(Long componentId, Long userId, String type) {
        // 首先验证组件是否存在且用户有权限
        return componentRepository.findByUserIdAndId(userId, componentId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("组件不存在或无权限访问")))
                .flatMapMany(component -> 
                    resourceRepository.findByComponentIdAndType(componentId, type)
                            .map(resourceMapper::toDto)
                );
    }

    /**
     * 更新资源
     * @param resourceId 资源ID
     * @param resource 更新的资源信息
     * @param userId 用户ID
     * @return 更新后的资源
     */
    public Mono<DevOpsResource> updateResource(Long resourceId, DevOpsResource resource, Long userId) {
        return resourceRepository.findByUserIdAndId(userId, resourceId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("资源不存在或无权限访问")))
                .flatMap(existingEntity -> {
                    // 检查名称是否发生变化
                    boolean nameChanged = !existingEntity.getName().equals(resource.getName());
                    // 检查路径是否发生变化
                    boolean pathChanged = resource.getPath() != null && 
                                        !resource.getPath().equals(existingEntity.getPath());

                    Mono<Void> validationMono = Mono.empty();

                    // 如果名称发生变化，检查新名称在组件中是否已存在
                    if (nameChanged) {
                        validationMono = validationMono.then(
                            resourceRepository.existsByComponentIdAndName(existingEntity.getComponentId(), resource.getName())
                                    .flatMap(exists -> {
                                        if (exists) {
                                            return Mono.error(new IllegalArgumentException("资源名称在该组件中已存在"));
                                        }
                                        return Mono.empty();
                                    })
                        );
                    }

                    // 如果路径发生变化，检查新路径是否已被使用
                    if (pathChanged && resource.getPath() != null && !resource.getPath().trim().isEmpty()) {
                        validationMono = validationMono.then(
                            resourceRepository.existsByComponentIdAndPath(existingEntity.getComponentId(), resource.getPath())
                                    .flatMap(exists -> {
                                        if (exists) {
                                            return Mono.error(new IllegalArgumentException("资源路径在该组件中已被使用"));
                                        }
                                        return Mono.empty();
                                    })
                        );
                    }

                    return validationMono.then(Mono.defer(() -> {
                        DevOpsResourceEntity updatedEntity = resourceMapper.toUpdateEntity(resource, existingEntity);
                        return resourceRepository.save(updatedEntity);
                    }));
                })
                .map(resourceMapper::toDto);
    }

    /**
     * 删除资源
     * @param resourceId 资源ID
     * @param userId 用户ID
     * @return 删除结果
     */
    public Mono<Boolean> deleteResource(Long resourceId, Long userId) {
        return resourceRepository.findByUserIdAndId(userId, resourceId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("资源不存在或无权限访问")))
                .flatMap(entity -> resourceRepository.deleteByUserIdAndId(userId, resourceId))
                .map(deletedCount -> deletedCount > 0);
    }

    /**
     * 搜索资源
     * @param userId 用户ID
     * @param keyword 搜索关键词
     * @return 资源列表
     */
    public Flux<DevOpsResource> searchResources(Long userId, String keyword) {
        String pattern = "%" + keyword + "%";
        return resourceRepository.findByUserIdAndNameContaining(userId, pattern)
                .map(resourceMapper::toDto);
    }

    /**
     * 在组件中搜索资源
     * @param componentId 组件ID
     * @param userId 用户ID
     * @param keyword 搜索关键词
     * @return 资源列表
     */
    public Flux<DevOpsResource> searchResourcesInComponent(Long componentId, Long userId, String keyword) {
        // 首先验证组件是否存在且用户有权限
        return componentRepository.findByUserIdAndId(userId, componentId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("组件不存在或无权限访问")))
                .flatMapMany(component -> {
                    String pattern = "%" + keyword + "%";
                    return resourceRepository.findByComponentIdAndNameContaining(componentId, pattern)
                            .map(resourceMapper::toDto);
                });
    }

    /**
     * 统计组件的资源数量
     * @param componentId 组件ID
     * @param userId 用户ID
     * @return 资源数量
     */
    public Mono<Long> countResourcesByComponent(Long componentId, Long userId) {
        // 首先验证组件是否存在且用户有权限
        return componentRepository.findByUserIdAndId(userId, componentId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("组件不存在或无权限访问")))
                .flatMap(component -> resourceRepository.countByComponentId(componentId));
    }

    /**
     * 统计用户的资源数量
     * @param userId 用户ID
     * @return 资源数量
     */
    public Mono<Long> countResources(Long userId) {
        return resourceRepository.countByUserId(userId);
    }

    /**
     * 统计指定类型的资源数量
     * @param userId 用户ID
     * @param type 资源类型
     * @return 资源数量
     */
    public Mono<Long> countResourcesByType(Long userId, String type) {
        return resourceRepository.countByUserIdAndType(userId, type);
    }

    /**
     * 获取所有资源类型
     * @param userId 用户ID
     * @return 资源类型列表
     */
    public Flux<String> getAllResourceTypes(Long userId) {
        return resourceRepository.findDistinctTypesByUserId(userId);
    }

    /**
     * 根据路径获取资源
     * @param path 资源路径
     * @param userId 用户ID
     * @return 资源信息
     */
    public Mono<DevOpsResource> getResourceByPath(String path, Long userId) {
        return resourceRepository.findByUserIdAndPath(userId, path)
                .map(resourceMapper::toDto)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("资源不存在或无权限访问")));
    }
}
