package com.example.springvueapp.service;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * 执行器服务抽象接口
 * 定义标准执行器操作方法，支持多种执行平台扩展
 */
public interface ExecutorService {

    /**
     * 提交部署任务
     * @param taskName 任务名称
     * @param deploymentCommand 部署命令
     * @param parameters 执行参数
     * @return 任务执行结果
     */
    Mono<Map<String, Object>> submitDeploymentTask(String taskName, String deploymentCommand, Map<String, Object> parameters);

    /**
     * 获取任务执行状态
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    Mono<Map<String, Object>> getTaskStatus(String taskId);

    /**
     * 停止任务执行
     * @param taskId 任务ID
     * @return 停止结果
     */
    Mono<Boolean> stopTask(String taskId);

    /**
     * 取消任务执行
     * @param taskId 任务ID
     * @return 取消结果
     */
    Mono<Boolean> cancelTask(String taskId);

    /**
     * 获取任务执行日志
     * @param taskId 任务ID
     * @return 日志内容
     */
    Mono<String> getTaskLogs(String taskId);

    /**
     * 获取任务执行结果
     * @param taskId 任务ID
     * @return 执行结果
     */
    Mono<Map<String, Object>> getTaskResult(String taskId);

    /**
     * 列出所有任务
     * @param namespace 命名空间
     * @return 任务列表
     */
    Flux<Map<String, Object>> listTasks(String namespace);

    /**
     * 清理已完成的任务
     * @param namespace 命名空间
     * @param keepCount 保留数量
     * @return 清理的任务数量
     */
    Mono<Integer> cleanupCompletedTasks(String namespace, int keepCount);

    /**
     * 创建命名空间
     * @param namespace 命名空间名称
     * @return 创建结果
     */
    Mono<Boolean> createNamespace(String namespace);

    /**
     * 删除命名空间
     * @param namespace 命名空间名称
     * @return 删除结果
     */
    Mono<Boolean> deleteNamespace(String namespace);

    /**
     * 获取命名空间列表
     * @return 命名空间列表
     */
    Flux<String> listNamespaces();

    /**
     * 检查执行器连接状态
     * @return 连接状态
     */
    Mono<Boolean> checkConnection();

    /**
     * 获取执行器信息
     * @return 执行器信息
     */
    Mono<Map<String, Object>> getExecutorInfo();

    /**
     * 验证部署命令
     * @param deploymentCommand 部署命令
     * @return 验证结果
     */
    Mono<Boolean> validateDeploymentCommand(String deploymentCommand);

    /**
     * 获取支持的命令类型
     * @return 命令类型列表
     */
    Flux<String> getSupportedCommandTypes();

    /**
     * 监控任务执行进度
     * @param taskId 任务ID
     * @return 进度信息流
     */
    Flux<Map<String, Object>> monitorTaskProgress(String taskId);

    /**
     * 获取任务执行指标
     * @param taskId 任务ID
     * @return 执行指标
     */
    Mono<Map<String, Object>> getTaskMetrics(String taskId);

    /**
     * 重试失败的任务
     * @param taskId 任务ID
     * @return 重试结果
     */
    Mono<Map<String, Object>> retryTask(String taskId);

    /**
     * 暂停任务执行
     * @param taskId 任务ID
     * @return 暂停结果
     */
    Mono<Boolean> pauseTask(String taskId);

    /**
     * 恢复任务执行
     * @param taskId 任务ID
     * @return 恢复结果
     */
    Mono<Boolean> resumeTask(String taskId);
}
