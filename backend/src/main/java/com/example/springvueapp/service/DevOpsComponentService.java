package com.example.springvueapp.service;

import com.example.springvueapp.entity.DevOpsComponentEntity;
import com.example.springvueapp.mapper.DevOpsComponentMapper;
import com.example.springvueapp.model.DevOpsComponent;
import com.example.springvueapp.repository.DevOpsApplicationRepository;
import com.example.springvueapp.repository.DevOpsComponentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * DevOps组件管理服务
 */
@Service
public class DevOpsComponentService {

    private final DevOpsComponentRepository componentRepository;
    private final DevOpsApplicationRepository applicationRepository;
    private final DevOpsComponentMapper componentMapper;

    @Autowired
    public DevOpsComponentService(DevOpsComponentRepository componentRepository,
                                 DevOpsApplicationRepository applicationRepository,
                                 DevOpsComponentMapper componentMapper) {
        this.componentRepository = componentRepository;
        this.applicationRepository = applicationRepository;
        this.componentMapper = componentMapper;
    }

    /**
     * 创建新组件
     * @param component 组件信息
     * @param applicationId 应用ID
     * @param userId 用户ID
     * @return 创建的组件
     */
    public Mono<DevOpsComponent> createComponent(DevOpsComponent component, Long applicationId, Long userId) {
        // 首先验证应用是否存在且用户有权限
        return applicationRepository.findByUserIdAndId(userId, applicationId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("应用不存在或无权限访问")))
                .flatMap(application -> 
                    componentRepository.existsByApplicationIdAndName(applicationId, component.getName())
                            .flatMap(exists -> {
                                if (exists) {
                                    return Mono.error(new IllegalArgumentException("组件名称在该应用中已存在"));
                                }
                                // 检查仓库URL是否已被使用（如果提供了仓库URL）
                                if (component.getRepositoryUrl() != null && !component.getRepositoryUrl().trim().isEmpty()) {
                                    return componentRepository.existsByRepositoryUrl(component.getRepositoryUrl())
                                            .flatMap(urlExists -> {
                                                if (urlExists) {
                                                    return Mono.error(new IllegalArgumentException("仓库URL已被其他组件使用"));
                                                }
                                                DevOpsComponentEntity entity = componentMapper.toNewEntity(component, applicationId, userId);
                                                return componentRepository.save(entity)
                                                        .map(componentMapper::toDto);
                                            });
                                } else {
                                    DevOpsComponentEntity entity = componentMapper.toNewEntity(component, applicationId, userId);
                                    return componentRepository.save(entity)
                                            .map(componentMapper::toDto);
                                }
                            })
                );
    }

    /**
     * 根据ID获取组件
     * @param componentId 组件ID
     * @param userId 用户ID
     * @return 组件信息
     */
    public Mono<DevOpsComponent> getComponentById(Long componentId, Long userId) {
        return componentRepository.findByUserIdAndId(userId, componentId)
                .map(componentMapper::toDto)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("组件不存在或无权限访问")));
    }

    /**
     * 获取用户的所有组件
     * @param userId 用户ID
     * @return 组件列表
     */
    public Flux<DevOpsComponent> getAllComponents(Long userId) {
        return componentRepository.findByUserId(userId)
                .map(componentMapper::toDto);
    }

    /**
     * 获取应用下的所有组件
     * @param applicationId 应用ID
     * @param userId 用户ID
     * @return 组件列表
     */
    public Flux<DevOpsComponent> getComponentsByApplication(Long applicationId, Long userId) {
        // 首先验证应用是否存在且用户有权限
        return applicationRepository.findByUserIdAndId(userId, applicationId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("应用不存在或无权限访问")))
                .flatMapMany(application -> 
                    componentRepository.findByUserIdAndApplicationId(userId, applicationId)
                            .map(componentMapper::toDto)
                );
    }

    /**
     * 根据状态获取组件
     * @param userId 用户ID
     * @param status 组件状态
     * @return 组件列表
     */
    public Flux<DevOpsComponent> getComponentsByStatus(Long userId, String status) {
        return componentRepository.findByUserIdAndStatus(userId, status)
                .map(componentMapper::toDto);
    }

    /**
     * 根据仓库类型获取组件
     * @param userId 用户ID
     * @param repositoryType 仓库类型
     * @return 组件列表
     */
    public Flux<DevOpsComponent> getComponentsByRepositoryType(Long userId, String repositoryType) {
        return componentRepository.findByUserIdAndRepositoryType(userId, repositoryType)
                .map(componentMapper::toDto);
    }

    /**
     * 更新组件
     * @param componentId 组件ID
     * @param component 更新的组件信息
     * @param userId 用户ID
     * @return 更新后的组件
     */
    public Mono<DevOpsComponent> updateComponent(Long componentId, DevOpsComponent component, Long userId) {
        return componentRepository.findByUserIdAndId(userId, componentId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("组件不存在或无权限访问")))
                .flatMap(existingEntity -> {
                    // 检查名称是否发生变化
                    boolean nameChanged = !existingEntity.getName().equals(component.getName());
                    // 检查仓库URL是否发生变化
                    boolean urlChanged = component.getRepositoryUrl() != null && 
                                       !component.getRepositoryUrl().equals(existingEntity.getRepositoryUrl());

                    Mono<Void> validationMono = Mono.empty();

                    // 如果名称发生变化，检查新名称在应用中是否已存在
                    if (nameChanged) {
                        validationMono = validationMono.then(
                            componentRepository.existsByApplicationIdAndName(existingEntity.getApplicationId(), component.getName())
                                    .flatMap(exists -> {
                                        if (exists) {
                                            return Mono.error(new IllegalArgumentException("组件名称在该应用中已存在"));
                                        }
                                        return Mono.empty();
                                    })
                        );
                    }

                    // 如果仓库URL发生变化，检查新URL是否已被使用
                    if (urlChanged && component.getRepositoryUrl() != null && !component.getRepositoryUrl().trim().isEmpty()) {
                        validationMono = validationMono.then(
                            componentRepository.existsByRepositoryUrl(component.getRepositoryUrl())
                                    .flatMap(exists -> {
                                        if (exists) {
                                            return Mono.error(new IllegalArgumentException("仓库URL已被其他组件使用"));
                                        }
                                        return Mono.empty();
                                    })
                        );
                    }

                    return validationMono.then(Mono.defer(() -> {
                        DevOpsComponentEntity updatedEntity = componentMapper.toUpdateEntity(component, existingEntity);
                        return componentRepository.save(updatedEntity);
                    }));
                })
                .map(componentMapper::toDto);
    }

    /**
     * 删除组件
     * @param componentId 组件ID
     * @param userId 用户ID
     * @return 删除结果
     */
    public Mono<Boolean> deleteComponent(Long componentId, Long userId) {
        return componentRepository.findByUserIdAndId(userId, componentId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("组件不存在或无权限访问")))
                .flatMap(entity -> componentRepository.deleteByUserIdAndId(userId, componentId))
                .map(deletedCount -> deletedCount > 0);
    }

    /**
     * 搜索组件
     * @param userId 用户ID
     * @param keyword 搜索关键词
     * @return 组件列表
     */
    public Flux<DevOpsComponent> searchComponents(Long userId, String keyword) {
        String pattern = "%" + keyword + "%";
        return componentRepository.findByUserIdAndNameContaining(userId, pattern)
                .map(componentMapper::toDto);
    }

    /**
     * 在应用中搜索组件
     * @param applicationId 应用ID
     * @param userId 用户ID
     * @param keyword 搜索关键词
     * @return 组件列表
     */
    public Flux<DevOpsComponent> searchComponentsInApplication(Long applicationId, Long userId, String keyword) {
        // 首先验证应用是否存在且用户有权限
        return applicationRepository.findByUserIdAndId(userId, applicationId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("应用不存在或无权限访问")))
                .flatMapMany(application -> {
                    String pattern = "%" + keyword + "%";
                    return componentRepository.findByApplicationIdAndNameContaining(applicationId, pattern)
                            .map(componentMapper::toDto);
                });
    }

    /**
     * 统计应用的组件数量
     * @param applicationId 应用ID
     * @param userId 用户ID
     * @return 组件数量
     */
    public Mono<Long> countComponentsByApplication(Long applicationId, Long userId) {
        // 首先验证应用是否存在且用户有权限
        return applicationRepository.findByUserIdAndId(userId, applicationId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("应用不存在或无权限访问")))
                .flatMap(application -> componentRepository.countByApplicationId(applicationId));
    }

    /**
     * 统计用户的组件数量
     * @param userId 用户ID
     * @return 组件数量
     */
    public Mono<Long> countComponents(Long userId) {
        return componentRepository.countByUserId(userId);
    }

    /**
     * 根据仓库URL获取组件
     * @param repositoryUrl 仓库URL
     * @param userId 用户ID
     * @return 组件信息
     */
    public Mono<DevOpsComponent> getComponentByRepositoryUrl(String repositoryUrl, Long userId) {
        return componentRepository.findByUserIdAndRepositoryUrl(userId, repositoryUrl)
                .map(componentMapper::toDto)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("组件不存在或无权限访问")));
    }
}
