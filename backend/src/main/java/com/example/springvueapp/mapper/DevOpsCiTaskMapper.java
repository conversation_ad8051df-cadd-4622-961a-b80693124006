package com.example.springvueapp.mapper;

import com.example.springvueapp.entity.DevOpsCiTaskEntity;
import com.example.springvueapp.model.DevOpsCiTask;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * DevOps CI任务的实体与DTO转换器
 */
@Component
public class DevOpsCiTaskMapper {

    private final ObjectMapper objectMapper;

    @Autowired
    public DevOpsCiTaskMapper(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * 将实体转换为DTO
     * @param entity CI任务实体
     * @return CI任务DTO
     */
    public DevOpsCiTask toDto(DevOpsCiTaskEntity entity) {
        if (entity == null) {
            return null;
        }

        Map<String, Object> configuration = parseConfiguration(entity.getConfiguration());

        return DevOpsCiTask.builder()
                .id(entity.getId())
                .name(entity.getName())
                .description(entity.getDescription())
                .componentId(entity.getComponentId())
                .version(entity.getVersion())
                .taskType(entity.getTaskType())
                .status(entity.getStatus())
                .configuration(configuration)
                .userId(entity.getUserId())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }

    /**
     * 将DTO转换为实体
     * @param dto CI任务DTO
     * @return CI任务实体
     */
    public DevOpsCiTaskEntity toEntity(DevOpsCiTask dto) {
        if (dto == null) {
            return null;
        }

        String configurationJson = serializeConfiguration(dto.getConfiguration());

        return DevOpsCiTaskEntity.builder()
                .id(dto.getId())
                .name(dto.getName())
                .description(dto.getDescription())
                .componentId(dto.getComponentId())
                .version(dto.getVersion())
                .taskType(dto.getTaskType())
                .status(dto.getStatus())
                .configuration(configurationJson)
                .userId(dto.getUserId())
                .createdAt(dto.getCreatedAt())
                .updatedAt(dto.getUpdatedAt())
                .build();
    }

    /**
     * 将DTO转换为新建实体（不包含ID和时间戳）
     * @param dto CI任务DTO
     * @param componentId 组件ID
     * @param userId 用户ID
     * @return CI任务实体
     */
    public DevOpsCiTaskEntity toNewEntity(DevOpsCiTask dto, Long componentId, Long userId) {
        if (dto == null) {
            return null;
        }

        LocalDateTime now = LocalDateTime.now();
        String configurationJson = serializeConfiguration(dto.getConfiguration());

        return DevOpsCiTaskEntity.builder()
                .name(dto.getName())
                .description(dto.getDescription())
                .componentId(componentId)
                .version(dto.getVersion())
                .taskType(dto.getTaskType())
                .status(dto.getStatus() != null ? dto.getStatus() : "INACTIVE")
                .configuration(configurationJson)
                .userId(userId)
                .createdAt(now)
                .updatedAt(now)
                .build();
    }

    /**
     * 将DTO转换为更新实体（保留ID，更新时间戳）
     * @param dto CI任务DTO
     * @param existingEntity 现有实体
     * @return 更新后的CI任务实体
     */
    public DevOpsCiTaskEntity toUpdateEntity(DevOpsCiTask dto, DevOpsCiTaskEntity existingEntity) {
        if (dto == null || existingEntity == null) {
            return null;
        }

        String configurationJson = dto.getConfiguration() != null ? 
                serializeConfiguration(dto.getConfiguration()) : existingEntity.getConfiguration();

        return DevOpsCiTaskEntity.builder()
                .id(existingEntity.getId())
                .name(dto.getName() != null ? dto.getName() : existingEntity.getName())
                .description(dto.getDescription() != null ? dto.getDescription() : existingEntity.getDescription())
                .componentId(existingEntity.getComponentId())
                .version(dto.getVersion() != null ? dto.getVersion() : existingEntity.getVersion())
                .taskType(dto.getTaskType() != null ? dto.getTaskType() : existingEntity.getTaskType())
                .status(dto.getStatus() != null ? dto.getStatus() : existingEntity.getStatus())
                .configuration(configurationJson)
                .userId(existingEntity.getUserId())
                .createdAt(existingEntity.getCreatedAt())
                .updatedAt(LocalDateTime.now())
                .build();
    }

    /**
     * 解析配置JSON字符串为Map
     * @param configurationJson JSON字符串
     * @return 配置Map
     */
    private Map<String, Object> parseConfiguration(String configurationJson) {
        if (configurationJson == null || configurationJson.trim().isEmpty()) {
            return new HashMap<>();
        }

        try {
            return objectMapper.readValue(configurationJson, new TypeReference<Map<String, Object>>() {});
        } catch (JsonProcessingException e) {
            // 如果解析失败，返回空Map并记录错误
            return new HashMap<>();
        }
    }

    /**
     * 序列化配置Map为JSON字符串
     * @param configuration 配置Map
     * @return JSON字符串
     */
    private String serializeConfiguration(Map<String, Object> configuration) {
        if (configuration == null || configuration.isEmpty()) {
            return "{}";
        }

        try {
            return objectMapper.writeValueAsString(configuration);
        } catch (JsonProcessingException e) {
            // 如果序列化失败，返回空JSON对象
            return "{}";
        }
    }
}
