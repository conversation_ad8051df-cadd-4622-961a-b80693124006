package com.example.springvueapp.mapper;

import com.example.springvueapp.entity.DevOpsApplicationEntity;
import com.example.springvueapp.model.DevOpsApplication;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * DevOps应用的实体与DTO转换器
 */
@Component
public class DevOpsApplicationMapper {

    /**
     * 将实体转换为DTO
     * @param entity 应用实体
     * @return 应用DTO
     */
    public DevOpsApplication toDto(DevOpsApplicationEntity entity) {
        if (entity == null) {
            return null;
        }

        return DevOpsApplication.builder()
                .id(entity.getId())
                .name(entity.getName())
                .description(entity.getDescription())
                .projectId(entity.getProjectId())
                .status(entity.getStatus())
                .userId(entity.getUserId())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }

    /**
     * 将DTO转换为实体
     * @param dto 应用DTO
     * @return 应用实体
     */
    public DevOpsApplicationEntity toEntity(DevOpsApplication dto) {
        if (dto == null) {
            return null;
        }

        return DevOpsApplicationEntity.builder()
                .id(dto.getId())
                .name(dto.getName())
                .description(dto.getDescription())
                .projectId(dto.getProjectId())
                .status(dto.getStatus())
                .userId(dto.getUserId())
                .createdAt(dto.getCreatedAt())
                .updatedAt(dto.getUpdatedAt())
                .build();
    }

    /**
     * 将DTO转换为新建实体（不包含ID和时间戳）
     * @param dto 应用DTO
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 应用实体
     */
    public DevOpsApplicationEntity toNewEntity(DevOpsApplication dto, Long projectId, Long userId) {
        if (dto == null) {
            return null;
        }

        LocalDateTime now = LocalDateTime.now();
        return DevOpsApplicationEntity.builder()
                .name(dto.getName())
                .description(dto.getDescription())
                .projectId(projectId)
                .status(dto.getStatus() != null ? dto.getStatus() : "ACTIVE")
                .userId(userId)
                .createdAt(now)
                .updatedAt(now)
                .build();
    }

    /**
     * 将DTO转换为更新实体（保留ID，更新时间戳）
     * @param dto 应用DTO
     * @param existingEntity 现有实体
     * @return 更新后的应用实体
     */
    public DevOpsApplicationEntity toUpdateEntity(DevOpsApplication dto, DevOpsApplicationEntity existingEntity) {
        if (dto == null || existingEntity == null) {
            return null;
        }

        return DevOpsApplicationEntity.builder()
                .id(existingEntity.getId())
                .name(dto.getName() != null ? dto.getName() : existingEntity.getName())
                .description(dto.getDescription() != null ? dto.getDescription() : existingEntity.getDescription())
                .projectId(existingEntity.getProjectId())
                .status(dto.getStatus() != null ? dto.getStatus() : existingEntity.getStatus())
                .userId(existingEntity.getUserId())
                .createdAt(existingEntity.getCreatedAt())
                .updatedAt(LocalDateTime.now())
                .build();
    }
}
