package com.example.springvueapp.mapper;

import com.example.springvueapp.entity.DevOpsComponentEntity;
import com.example.springvueapp.model.DevOpsComponent;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * DevOps组件的实体与DTO转换器
 */
@Component
public class DevOpsComponentMapper {

    /**
     * 将实体转换为DTO
     * @param entity 组件实体
     * @return 组件DTO
     */
    public DevOpsComponent toDto(DevOpsComponentEntity entity) {
        if (entity == null) {
            return null;
        }

        return DevOpsComponent.builder()
                .id(entity.getId())
                .name(entity.getName())
                .description(entity.getDescription())
                .applicationId(entity.getApplicationId())
                .repositoryUrl(entity.getRepositoryUrl())
                .repositoryType(entity.getRepositoryType())
                .status(entity.getStatus())
                .userId(entity.getUserId())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }

    /**
     * 将DTO转换为实体
     * @param dto 组件DTO
     * @return 组件实体
     */
    public DevOpsComponentEntity toEntity(DevOpsComponent dto) {
        if (dto == null) {
            return null;
        }

        return DevOpsComponentEntity.builder()
                .id(dto.getId())
                .name(dto.getName())
                .description(dto.getDescription())
                .applicationId(dto.getApplicationId())
                .repositoryUrl(dto.getRepositoryUrl())
                .repositoryType(dto.getRepositoryType())
                .status(dto.getStatus())
                .userId(dto.getUserId())
                .createdAt(dto.getCreatedAt())
                .updatedAt(dto.getUpdatedAt())
                .build();
    }

    /**
     * 将DTO转换为新建实体（不包含ID和时间戳）
     * @param dto 组件DTO
     * @param applicationId 应用ID
     * @param userId 用户ID
     * @return 组件实体
     */
    public DevOpsComponentEntity toNewEntity(DevOpsComponent dto, Long applicationId, Long userId) {
        if (dto == null) {
            return null;
        }

        LocalDateTime now = LocalDateTime.now();
        return DevOpsComponentEntity.builder()
                .name(dto.getName())
                .description(dto.getDescription())
                .applicationId(applicationId)
                .repositoryUrl(dto.getRepositoryUrl())
                .repositoryType(dto.getRepositoryType() != null ? dto.getRepositoryType() : "GIT")
                .status(dto.getStatus() != null ? dto.getStatus() : "ACTIVE")
                .userId(userId)
                .createdAt(now)
                .updatedAt(now)
                .build();
    }

    /**
     * 将DTO转换为更新实体（保留ID，更新时间戳）
     * @param dto 组件DTO
     * @param existingEntity 现有实体
     * @return 更新后的组件实体
     */
    public DevOpsComponentEntity toUpdateEntity(DevOpsComponent dto, DevOpsComponentEntity existingEntity) {
        if (dto == null || existingEntity == null) {
            return null;
        }

        return DevOpsComponentEntity.builder()
                .id(existingEntity.getId())
                .name(dto.getName() != null ? dto.getName() : existingEntity.getName())
                .description(dto.getDescription() != null ? dto.getDescription() : existingEntity.getDescription())
                .applicationId(existingEntity.getApplicationId())
                .repositoryUrl(dto.getRepositoryUrl() != null ? dto.getRepositoryUrl() : existingEntity.getRepositoryUrl())
                .repositoryType(dto.getRepositoryType() != null ? dto.getRepositoryType() : existingEntity.getRepositoryType())
                .status(dto.getStatus() != null ? dto.getStatus() : existingEntity.getStatus())
                .userId(existingEntity.getUserId())
                .createdAt(existingEntity.getCreatedAt())
                .updatedAt(LocalDateTime.now())
                .build();
    }
}
