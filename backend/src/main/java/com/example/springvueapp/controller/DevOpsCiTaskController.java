package com.example.springvueapp.controller;

import com.example.springvueapp.model.DevOpsCiTask;
import com.example.springvueapp.model.DevOpsCiTaskInstance;
import com.example.springvueapp.service.CiTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import jakarta.validation.Valid;
import java.util.Map;

/**
 * DevOps CI任务管理控制器
 */
@RestController
@RequestMapping("/api/devops/ci/tasks")
public class DevOpsCiTaskController {

    private final CiTaskService ciTaskService;

    @Autowired
    public DevOpsCiTaskController(CiTaskService ciTaskService) {
        this.ciTaskService = ciTaskService;
    }

    /**
     * 在指定组件中创建新CI任务
     * @param componentId 组件ID
     * @param ciTask CI任务信息
     * @param authentication 认证信息
     * @return 创建的CI任务
     */
    @PostMapping("/component/{componentId}")
    public Mono<ResponseEntity<DevOpsCiTask>> createCiTask(
            @PathVariable Long componentId,
            @Valid @RequestBody DevOpsCiTask ciTask,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return ciTaskService.createCiTask(ciTask, componentId, userId)
                .map(createdTask -> ResponseEntity.status(HttpStatus.CREATED).body(createdTask))
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.badRequest().build());
    }

    /**
     * 获取所有CI任务
     * @param authentication 认证信息
     * @return CI任务列表
     */
    @GetMapping
    public Flux<DevOpsCiTask> getAllCiTasks(Authentication authentication) {
        Long userId = getUserIdFromAuthentication(authentication);
        return ciTaskService.getAllCiTasks(userId);
    }

    /**
     * 获取指定组件下的所有CI任务
     * @param componentId 组件ID
     * @param authentication 认证信息
     * @return CI任务列表
     */
    @GetMapping("/component/{componentId}")
    public Flux<DevOpsCiTask> getCiTasksByComponent(
            @PathVariable Long componentId,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return ciTaskService.getCiTasksByComponent(componentId, userId)
                .onErrorResume(IllegalArgumentException.class, 
                    error -> Flux.empty());
    }

    /**
     * 根据ID获取CI任务
     * @param taskId CI任务ID
     * @param authentication 认证信息
     * @return CI任务信息
     */
    @GetMapping("/{taskId}")
    public Mono<ResponseEntity<DevOpsCiTask>> getCiTaskById(
            @PathVariable Long taskId,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return ciTaskService.getCiTaskById(taskId, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.notFound().build());
    }

    /**
     * 更新CI任务
     * @param taskId CI任务ID
     * @param ciTask 更新的CI任务信息
     * @param authentication 认证信息
     * @return 更新后的CI任务
     */
    @PutMapping("/{taskId}")
    public Mono<ResponseEntity<DevOpsCiTask>> updateCiTask(
            @PathVariable Long taskId,
            @Valid @RequestBody DevOpsCiTask ciTask,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return ciTaskService.updateCiTask(taskId, ciTask, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.badRequest().build());
    }

    /**
     * 删除CI任务
     * @param taskId CI任务ID
     * @param authentication 认证信息
     * @return 删除结果
     */
    @DeleteMapping("/{taskId}")
    public Mono<ResponseEntity<Void>> deleteCiTask(
            @PathVariable Long taskId,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return ciTaskService.deleteCiTask(taskId, userId)
                .map(deleted -> deleted ? 
                    ResponseEntity.noContent().<Void>build() : 
                    ResponseEntity.notFound().<Void>build())
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.notFound().build());
    }

    /**
     * 启动CI任务
     * @param taskId CI任务ID
     * @param parameters 启动参数
     * @param authentication 认证信息
     * @return 任务实例
     */
    @PostMapping("/{taskId}/start")
    public Mono<ResponseEntity<DevOpsCiTaskInstance>> startCiTask(
            @PathVariable Long taskId,
            @RequestBody(required = false) Map<String, Object> parameters,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return ciTaskService.startCiTask(taskId, userId, parameters)
                .map(instance -> ResponseEntity.status(HttpStatus.CREATED).body(instance))
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.badRequest().build());
    }

    /**
     * 停止CI任务实例
     * @param instanceId 实例ID
     * @param authentication 认证信息
     * @return 停止结果
     */
    @PostMapping("/instances/{instanceId}/stop")
    public Mono<ResponseEntity<Void>> stopCiTaskInstance(
            @PathVariable String instanceId,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return ciTaskService.stopCiTaskInstance(instanceId, userId)
                .map(stopped -> stopped ? 
                    ResponseEntity.ok().<Void>build() : 
                    ResponseEntity.notFound().<Void>build())
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.notFound().build());
    }

    /**
     * 取消CI任务实例
     * @param instanceId 实例ID
     * @param authentication 认证信息
     * @return 取消结果
     */
    @PostMapping("/instances/{instanceId}/cancel")
    public Mono<ResponseEntity<Void>> cancelCiTaskInstance(
            @PathVariable String instanceId,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return ciTaskService.cancelCiTaskInstance(instanceId, userId)
                .map(cancelled -> cancelled ? 
                    ResponseEntity.ok().<Void>build() : 
                    ResponseEntity.notFound().<Void>build())
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.notFound().build());
    }

    /**
     * 获取CI任务实例状态
     * @param instanceId 实例ID
     * @param authentication 认证信息
     * @return 任务实例信息
     */
    @GetMapping("/instances/{instanceId}")
    public Mono<ResponseEntity<DevOpsCiTaskInstance>> getCiTaskInstanceStatus(
            @PathVariable String instanceId,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return ciTaskService.getCiTaskInstanceStatus(instanceId, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.notFound().build());
    }

    /**
     * 获取CI任务的所有实例
     * @param taskId CI任务ID
     * @param authentication 认证信息
     * @return 任务实例列表
     */
    @GetMapping("/{taskId}/instances")
    public Flux<DevOpsCiTaskInstance> getCiTaskInstances(
            @PathVariable Long taskId,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return ciTaskService.getCiTaskInstances(taskId, userId)
                .onErrorResume(IllegalArgumentException.class, 
                    error -> Flux.empty());
    }

    /**
     * 获取CI任务实例日志
     * @param instanceId 实例ID
     * @param authentication 认证信息
     * @return 日志内容
     */
    @GetMapping("/instances/{instanceId}/logs")
    public Mono<ResponseEntity<String>> getCiTaskInstanceLogs(
            @PathVariable String instanceId,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return ciTaskService.getCiTaskInstanceLogs(instanceId, userId)
                .map(logs -> ResponseEntity.ok().body(logs))
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.notFound().build());
    }

    /**
     * 清理已完成的CI任务实例
     * @param taskId CI任务ID
     * @param keepCount 保留的实例数量
     * @param authentication 认证信息
     * @return 清理的实例数量
     */
    @DeleteMapping("/{taskId}/instances/cleanup")
    public Mono<ResponseEntity<Integer>> cleanupCompletedInstances(
            @PathVariable Long taskId,
            @RequestParam(defaultValue = "10") int keepCount,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return ciTaskService.cleanupCompletedInstances(taskId, userId, keepCount)
                .map(ResponseEntity::ok)
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.notFound().build());
    }

    /**
     * 获取CI任务模板
     * @param taskType 任务类型
     * @return 模板配置
     */
    @GetMapping("/templates/{taskType}")
    public Mono<ResponseEntity<Map<String, Object>>> getTaskTemplate(
            @PathVariable String taskType) {
        
        return ciTaskService.getTaskTemplate(taskType)
                .map(ResponseEntity::ok);
    }

    /**
     * 获取支持的任务类型
     * @return 任务类型列表
     */
    @GetMapping("/types")
    public Flux<String> getSupportedTaskTypes() {
        return ciTaskService.getSupportedTaskTypes();
    }

    /**
     * 检查CI平台连接状态
     * @return 连接状态
     */
    @GetMapping("/platform/status")
    public Mono<ResponseEntity<Boolean>> checkConnection() {
        return ciTaskService.checkConnection()
                .map(ResponseEntity::ok);
    }

    /**
     * 获取CI平台信息
     * @return 平台信息
     */
    @GetMapping("/platform/info")
    public Mono<ResponseEntity<Map<String, Object>>> getPlatformInfo() {
        return ciTaskService.getPlatformInfo()
                .map(ResponseEntity::ok);
    }

    /**
     * 从认证信息中获取用户ID
     * @param authentication 认证信息
     * @return 用户ID
     */
    private Long getUserIdFromAuthentication(Authentication authentication) {
        if (authentication == null || authentication.getName() == null) {
            return 1L; // 测试环境默认用户ID
        }
        try {
            return Long.parseLong(authentication.getName());
        } catch (NumberFormatException e) {
            return 1L;
        }
    }
}
