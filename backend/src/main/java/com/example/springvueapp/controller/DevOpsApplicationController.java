package com.example.springvueapp.controller;

import com.example.springvueapp.model.DevOpsApplication;
import com.example.springvueapp.service.DevOpsApplicationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import jakarta.validation.Valid;

/**
 * DevOps应用管理控制器
 */
@RestController
@RequestMapping("/api/devops/applications")
public class DevOpsApplicationController {

    private final DevOpsApplicationService applicationService;

    @Autowired
    public DevOpsApplicationController(DevOpsApplicationService applicationService) {
        this.applicationService = applicationService;
    }

    /**
     * 在指定项目中创建新应用
     * @param projectId 项目ID
     * @param application 应用信息
     * @param authentication 认证信息
     * @return 创建的应用
     */
    @PostMapping("/project/{projectId}")
    public Mono<ResponseEntity<DevOpsApplication>> createApplication(
            @PathVariable Long projectId,
            @Valid @RequestBody DevOpsApplication application,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return applicationService.createApplication(application, projectId, userId)
                .map(createdApplication -> ResponseEntity.status(HttpStatus.CREATED).body(createdApplication))
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.badRequest().build());
    }

    /**
     * 获取所有应用
     * @param authentication 认证信息
     * @return 应用列表
     */
    @GetMapping
    public Flux<DevOpsApplication> getAllApplications(Authentication authentication) {
        Long userId = getUserIdFromAuthentication(authentication);
        return applicationService.getAllApplications(userId);
    }

    /**
     * 获取指定项目下的所有应用
     * @param projectId 项目ID
     * @param authentication 认证信息
     * @return 应用列表
     */
    @GetMapping("/project/{projectId}")
    public Flux<DevOpsApplication> getApplicationsByProject(
            @PathVariable Long projectId,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return applicationService.getApplicationsByProject(projectId, userId)
                .onErrorResume(IllegalArgumentException.class, 
                    error -> Flux.empty());
    }

    /**
     * 根据ID获取应用
     * @param applicationId 应用ID
     * @param authentication 认证信息
     * @return 应用信息
     */
    @GetMapping("/{applicationId}")
    public Mono<ResponseEntity<DevOpsApplication>> getApplicationById(
            @PathVariable Long applicationId,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return applicationService.getApplicationById(applicationId, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.notFound().build());
    }

    /**
     * 更新应用
     * @param applicationId 应用ID
     * @param application 更新的应用信息
     * @param authentication 认证信息
     * @return 更新后的应用
     */
    @PutMapping("/{applicationId}")
    public Mono<ResponseEntity<DevOpsApplication>> updateApplication(
            @PathVariable Long applicationId,
            @Valid @RequestBody DevOpsApplication application,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return applicationService.updateApplication(applicationId, application, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.badRequest().build());
    }

    /**
     * 删除应用
     * @param applicationId 应用ID
     * @param authentication 认证信息
     * @return 删除结果
     */
    @DeleteMapping("/{applicationId}")
    public Mono<ResponseEntity<Void>> deleteApplication(
            @PathVariable Long applicationId,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return applicationService.deleteApplication(applicationId, userId)
                .then(Mono.just(ResponseEntity.noContent().<Void>build()))
                .onErrorReturn(IllegalArgumentException.class,
                    ResponseEntity.notFound().build());
    }

    /**
     * 根据状态获取应用
     * @param status 应用状态
     * @param authentication 认证信息
     * @return 应用列表
     */
    @GetMapping("/status/{status}")
    public Flux<DevOpsApplication> getApplicationsByStatus(
            @PathVariable String status,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return applicationService.getApplicationsByStatus(userId, status);
    }

    /**
     * 搜索应用
     * @param keyword 搜索关键词
     * @param authentication 认证信息
     * @return 应用列表
     */
    @GetMapping("/search")
    public Flux<DevOpsApplication> searchApplications(
            @RequestParam String keyword,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return applicationService.searchApplications(userId, keyword);
    }

    /**
     * 在指定项目中搜索应用
     * @param projectId 项目ID
     * @param keyword 搜索关键词
     * @param authentication 认证信息
     * @return 应用列表
     */
    @GetMapping("/project/{projectId}/search")
    public Flux<DevOpsApplication> searchApplicationsInProject(
            @PathVariable Long projectId,
            @RequestParam String keyword,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return applicationService.searchApplicationsInProject(projectId, userId, keyword)
                .onErrorResume(IllegalArgumentException.class, 
                    error -> Flux.empty());
    }

    /**
     * 统计应用数量
     * @param authentication 认证信息
     * @return 应用数量
     */
    @GetMapping("/count")
    public Mono<Long> countApplications(Authentication authentication) {
        Long userId = getUserIdFromAuthentication(authentication);
        return applicationService.countApplications(userId);
    }

    /**
     * 统计指定项目的应用数量
     * @param projectId 项目ID
     * @param authentication 认证信息
     * @return 应用数量
     */
    @GetMapping("/project/{projectId}/count")
    public Mono<ResponseEntity<Long>> countApplicationsByProject(
            @PathVariable Long projectId,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return applicationService.countApplicationsByProject(projectId, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.notFound().build());
    }

    /**
     * 从认证信息中获取用户ID
     * @param authentication 认证信息
     * @return 用户ID
     */
    private Long getUserIdFromAuthentication(Authentication authentication) {
        // 这里假设用户名就是用户ID，实际项目中可能需要从用户服务获取
        // 或者从JWT token中解析用户ID
        try {
            return Long.parseLong(authentication.getName());
        } catch (NumberFormatException e) {
            // 如果用户名不是数字，可能需要通过用户名查询用户ID
            // 这里暂时返回1作为默认值，实际项目中需要实现用户ID获取逻辑
            return 1L;
        }
    }
}
