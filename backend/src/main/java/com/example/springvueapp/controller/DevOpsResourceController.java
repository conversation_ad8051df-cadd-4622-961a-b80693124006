package com.example.springvueapp.controller;

import com.example.springvueapp.model.DevOpsResource;
import com.example.springvueapp.service.DevOpsResourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import jakarta.validation.Valid;

/**
 * DevOps资源管理控制器
 */
@RestController
@RequestMapping("/api/devops/resources")
public class DevOpsResourceController {

    private final DevOpsResourceService resourceService;

    @Autowired
    public DevOpsResourceController(DevOpsResourceService resourceService) {
        this.resourceService = resourceService;
    }

    /**
     * 在指定组件中创建新资源
     * @param componentId 组件ID
     * @param resource 资源信息
     * @param authentication 认证信息
     * @return 创建的资源
     */
    @PostMapping("/component/{componentId}")
    public Mono<ResponseEntity<DevOpsResource>> createResource(
            @PathVariable Long componentId,
            @Valid @RequestBody DevOpsResource resource,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return resourceService.createResource(resource, componentId, userId)
                .map(createdResource -> ResponseEntity.status(HttpStatus.CREATED).body(createdResource))
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.badRequest().build());
    }

    /**
     * 获取所有资源
     * @param authentication 认证信息
     * @return 资源列表
     */
    @GetMapping
    public Flux<DevOpsResource> getAllResources(Authentication authentication) {
        Long userId = getUserIdFromAuthentication(authentication);
        return resourceService.getAllResources(userId);
    }

    /**
     * 获取指定组件下的所有资源
     * @param componentId 组件ID
     * @param authentication 认证信息
     * @return 资源列表
     */
    @GetMapping("/component/{componentId}")
    public Flux<DevOpsResource> getResourcesByComponent(
            @PathVariable Long componentId,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return resourceService.getResourcesByComponent(componentId, userId)
                .onErrorResume(IllegalArgumentException.class, 
                    error -> Flux.empty());
    }

    /**
     * 根据ID获取资源
     * @param resourceId 资源ID
     * @param authentication 认证信息
     * @return 资源信息
     */
    @GetMapping("/{resourceId}")
    public Mono<ResponseEntity<DevOpsResource>> getResourceById(
            @PathVariable Long resourceId,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return resourceService.getResourceById(resourceId, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.notFound().build());
    }

    /**
     * 更新资源
     * @param resourceId 资源ID
     * @param resource 更新的资源信息
     * @param authentication 认证信息
     * @return 更新后的资源
     */
    @PutMapping("/{resourceId}")
    public Mono<ResponseEntity<DevOpsResource>> updateResource(
            @PathVariable Long resourceId,
            @Valid @RequestBody DevOpsResource resource,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return resourceService.updateResource(resourceId, resource, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.badRequest().build());
    }

    /**
     * 删除资源
     * @param resourceId 资源ID
     * @param authentication 认证信息
     * @return 删除结果
     */
    @DeleteMapping("/{resourceId}")
    public Mono<ResponseEntity<Void>> deleteResource(
            @PathVariable Long resourceId,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return resourceService.deleteResource(resourceId, userId)
                .then(Mono.just(ResponseEntity.noContent().<Void>build()))
                .onErrorReturn(IllegalArgumentException.class,
                    ResponseEntity.notFound().build());
    }

    /**
     * 根据状态获取资源
     * @param status 资源状态
     * @param authentication 认证信息
     * @return 资源列表
     */
    @GetMapping("/status/{status}")
    public Flux<DevOpsResource> getResourcesByStatus(
            @PathVariable String status,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return resourceService.getResourcesByStatus(userId, status);
    }

    /**
     * 根据类型获取资源
     * @param type 资源类型
     * @param authentication 认证信息
     * @return 资源列表
     */
    @GetMapping("/type/{type}")
    public Flux<DevOpsResource> getResourcesByType(
            @PathVariable String type,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return resourceService.getResourcesByType(userId, type);
    }

    /**
     * 根据组件ID和类型获取资源
     * @param componentId 组件ID
     * @param type 资源类型
     * @param authentication 认证信息
     * @return 资源列表
     */
    @GetMapping("/component/{componentId}/type/{type}")
    public Flux<DevOpsResource> getResourcesByComponentAndType(
            @PathVariable Long componentId,
            @PathVariable String type,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return resourceService.getResourcesByComponentAndType(componentId, userId, type)
                .onErrorResume(IllegalArgumentException.class, 
                    error -> Flux.empty());
    }

    /**
     * 搜索资源
     * @param keyword 搜索关键词
     * @param authentication 认证信息
     * @return 资源列表
     */
    @GetMapping("/search")
    public Flux<DevOpsResource> searchResources(
            @RequestParam String keyword,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return resourceService.searchResources(userId, keyword);
    }

    /**
     * 在指定组件中搜索资源
     * @param componentId 组件ID
     * @param keyword 搜索关键词
     * @param authentication 认证信息
     * @return 资源列表
     */
    @GetMapping("/component/{componentId}/search")
    public Flux<DevOpsResource> searchResourcesInComponent(
            @PathVariable Long componentId,
            @RequestParam String keyword,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return resourceService.searchResourcesInComponent(componentId, userId, keyword)
                .onErrorResume(IllegalArgumentException.class, 
                    error -> Flux.empty());
    }

    /**
     * 根据路径获取资源
     * @param path 资源路径
     * @param authentication 认证信息
     * @return 资源信息
     */
    @GetMapping("/path")
    public Mono<ResponseEntity<DevOpsResource>> getResourceByPath(
            @RequestParam String path,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return resourceService.getResourceByPath(path, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.notFound().build());
    }

    /**
     * 统计资源数量
     * @param authentication 认证信息
     * @return 资源数量
     */
    @GetMapping("/count")
    public Mono<Long> countResources(Authentication authentication) {
        Long userId = getUserIdFromAuthentication(authentication);
        return resourceService.countResources(userId);
    }

    /**
     * 统计指定组件的资源数量
     * @param componentId 组件ID
     * @param authentication 认证信息
     * @return 资源数量
     */
    @GetMapping("/component/{componentId}/count")
    public Mono<ResponseEntity<Long>> countResourcesByComponent(
            @PathVariable Long componentId,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return resourceService.countResourcesByComponent(componentId, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.notFound().build());
    }

    /**
     * 统计指定类型的资源数量
     * @param type 资源类型
     * @param authentication 认证信息
     * @return 资源数量
     */
    @GetMapping("/type/{type}/count")
    public Mono<Long> countResourcesByType(
            @PathVariable String type,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return resourceService.countResourcesByType(userId, type);
    }

    /**
     * 获取所有资源类型
     * @param authentication 认证信息
     * @return 资源类型列表
     */
    @GetMapping("/types")
    public Flux<String> getAllResourceTypes(Authentication authentication) {
        Long userId = getUserIdFromAuthentication(authentication);
        return resourceService.getAllResourceTypes(userId);
    }

    /**
     * 从认证信息中获取用户ID
     * @param authentication 认证信息
     * @return 用户ID
     */
    private Long getUserIdFromAuthentication(Authentication authentication) {
        // 这里假设用户名就是用户ID，实际项目中可能需要从用户服务获取
        // 或者从JWT token中解析用户ID
        try {
            return Long.parseLong(authentication.getName());
        } catch (NumberFormatException e) {
            // 如果用户名不是数字，可能需要通过用户名查询用户ID
            // 这里暂时返回1作为默认值，实际项目中需要实现用户ID获取逻辑
            return 1L;
        }
    }
}
