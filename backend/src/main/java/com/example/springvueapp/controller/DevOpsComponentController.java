package com.example.springvueapp.controller;

import com.example.springvueapp.model.DevOpsComponent;
import com.example.springvueapp.service.DevOpsComponentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import jakarta.validation.Valid;

/**
 * DevOps组件管理控制器
 */
@RestController
@RequestMapping("/api/devops/components")
public class DevOpsComponentController {

    private final DevOpsComponentService componentService;

    @Autowired
    public DevOpsComponentController(DevOpsComponentService componentService) {
        this.componentService = componentService;
    }

    /**
     * 在指定应用中创建新组件
     * @param applicationId 应用ID
     * @param component 组件信息
     * @param authentication 认证信息
     * @return 创建的组件
     */
    @PostMapping("/application/{applicationId}")
    public Mono<ResponseEntity<DevOpsComponent>> createComponent(
            @PathVariable Long applicationId,
            @Valid @RequestBody DevOpsComponent component,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return componentService.createComponent(component, applicationId, userId)
                .map(createdComponent -> ResponseEntity.status(HttpStatus.CREATED).body(createdComponent))
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.badRequest().build());
    }

    /**
     * 获取所有组件
     * @param authentication 认证信息
     * @return 组件列表
     */
    @GetMapping
    public Flux<DevOpsComponent> getAllComponents(Authentication authentication) {
        Long userId = getUserIdFromAuthentication(authentication);
        return componentService.getAllComponents(userId);
    }

    /**
     * 获取指定应用下的所有组件
     * @param applicationId 应用ID
     * @param authentication 认证信息
     * @return 组件列表
     */
    @GetMapping("/application/{applicationId}")
    public Flux<DevOpsComponent> getComponentsByApplication(
            @PathVariable Long applicationId,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return componentService.getComponentsByApplication(applicationId, userId)
                .onErrorResume(IllegalArgumentException.class, 
                    error -> Flux.empty());
    }

    /**
     * 根据ID获取组件
     * @param componentId 组件ID
     * @param authentication 认证信息
     * @return 组件信息
     */
    @GetMapping("/{componentId}")
    public Mono<ResponseEntity<DevOpsComponent>> getComponentById(
            @PathVariable Long componentId,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return componentService.getComponentById(componentId, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.notFound().build());
    }

    /**
     * 更新组件
     * @param componentId 组件ID
     * @param component 更新的组件信息
     * @param authentication 认证信息
     * @return 更新后的组件
     */
    @PutMapping("/{componentId}")
    public Mono<ResponseEntity<DevOpsComponent>> updateComponent(
            @PathVariable Long componentId,
            @Valid @RequestBody DevOpsComponent component,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return componentService.updateComponent(componentId, component, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.badRequest().build());
    }

    /**
     * 删除组件
     * @param componentId 组件ID
     * @param authentication 认证信息
     * @return 删除结果
     */
    @DeleteMapping("/{componentId}")
    public Mono<ResponseEntity<Void>> deleteComponent(
            @PathVariable Long componentId,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return componentService.deleteComponent(componentId, userId)
                .then(Mono.just(ResponseEntity.noContent().<Void>build()))
                .onErrorReturn(IllegalArgumentException.class,
                    ResponseEntity.notFound().build());
    }

    /**
     * 根据状态获取组件
     * @param status 组件状态
     * @param authentication 认证信息
     * @return 组件列表
     */
    @GetMapping("/status/{status}")
    public Flux<DevOpsComponent> getComponentsByStatus(
            @PathVariable String status,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return componentService.getComponentsByStatus(userId, status);
    }

    /**
     * 根据仓库类型获取组件
     * @param repositoryType 仓库类型
     * @param authentication 认证信息
     * @return 组件列表
     */
    @GetMapping("/repository-type/{repositoryType}")
    public Flux<DevOpsComponent> getComponentsByRepositoryType(
            @PathVariable String repositoryType,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return componentService.getComponentsByRepositoryType(userId, repositoryType);
    }

    /**
     * 搜索组件
     * @param keyword 搜索关键词
     * @param authentication 认证信息
     * @return 组件列表
     */
    @GetMapping("/search")
    public Flux<DevOpsComponent> searchComponents(
            @RequestParam String keyword,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return componentService.searchComponents(userId, keyword);
    }

    /**
     * 在指定应用中搜索组件
     * @param applicationId 应用ID
     * @param keyword 搜索关键词
     * @param authentication 认证信息
     * @return 组件列表
     */
    @GetMapping("/application/{applicationId}/search")
    public Flux<DevOpsComponent> searchComponentsInApplication(
            @PathVariable Long applicationId,
            @RequestParam String keyword,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return componentService.searchComponentsInApplication(applicationId, userId, keyword)
                .onErrorResume(IllegalArgumentException.class, 
                    error -> Flux.empty());
    }

    /**
     * 根据仓库URL获取组件
     * @param repositoryUrl 仓库URL
     * @param authentication 认证信息
     * @return 组件信息
     */
    @GetMapping("/repository")
    public Mono<ResponseEntity<DevOpsComponent>> getComponentByRepositoryUrl(
            @RequestParam String repositoryUrl,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return componentService.getComponentByRepositoryUrl(repositoryUrl, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.notFound().build());
    }

    /**
     * 统计组件数量
     * @param authentication 认证信息
     * @return 组件数量
     */
    @GetMapping("/count")
    public Mono<Long> countComponents(Authentication authentication) {
        Long userId = getUserIdFromAuthentication(authentication);
        return componentService.countComponents(userId);
    }

    /**
     * 统计指定应用的组件数量
     * @param applicationId 应用ID
     * @param authentication 认证信息
     * @return 组件数量
     */
    @GetMapping("/application/{applicationId}/count")
    public Mono<ResponseEntity<Long>> countComponentsByApplication(
            @PathVariable Long applicationId,
            Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        return componentService.countComponentsByApplication(applicationId, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.notFound().build());
    }

    /**
     * 从认证信息中获取用户ID
     * @param authentication 认证信息
     * @return 用户ID
     */
    private Long getUserIdFromAuthentication(Authentication authentication) {
        // 这里假设用户名就是用户ID，实际项目中可能需要从用户服务获取
        // 或者从JWT token中解析用户ID
        try {
            return Long.parseLong(authentication.getName());
        } catch (NumberFormatException e) {
            // 如果用户名不是数字，可能需要通过用户名查询用户ID
            // 这里暂时返回1作为默认值，实际项目中需要实现用户ID获取逻辑
            return 1L;
        }
    }
}
