<template>
  <div class="mcp-dashboard">
    <!-- Navigation Bar -->
    <nav class="bg-white shadow-sm border-b border-gray-200 mb-6">
      <div class="max-w-7xl mx-auto px-6">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center space-x-8">
            <h1 class="text-xl font-semibold text-gray-900">管理平台</h1>
            <div class="flex items-center space-x-6">
              <router-link
                to="/mcp"
                class="text-sm font-medium text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md"
                :class="{ 'bg-gray-100 text-gray-900': $route.path.startsWith('/mcp') }"
              >
                MCP 配置
              </router-link>
              <router-link
                to="/devops"
                class="text-sm font-medium text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md"
                :class="{ 'bg-gray-100 text-gray-900': $route.path.startsWith('/devops') }"
              >
                DevOps 管理
              </router-link>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-600">欢迎，{{ currentUser }}</span>
            <button
              @click="logout"
              class="text-sm text-gray-500 hover:text-gray-700"
            >
              登出
            </button>
          </div>
        </div>
      </div>
    </nav>

    <div class="dashboard-header">
      <h2 class="text-3xl font-bold text-gray-900">MCP 服务器仪表板</h2>
      <button
        @click="showCreateModal = true"
        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium"
      >
        添加新服务器
      </button>
    </div>

    <!-- Error Alert -->
    <div v-if="mcpStore.error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm text-red-800">{{ mcpStore.error }}</p>
        </div>
        <div class="ml-auto pl-3">
          <button @click="mcpStore.clearError()" class="text-red-400 hover:text-red-600">
            <span class="sr-only">Dismiss</span>
            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总配置数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ mcpStore.configurations.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">已启用</p>
            <p class="text-2xl font-semibold text-gray-900">{{ mcpStore.enabledConfigurations.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">运行中实例</p>
            <p class="text-2xl font-semibold text-gray-900">{{ mcpStore.runningInstances.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总实例数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ mcpStore.instances.length }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Configurations Table -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">服务器配置</h2>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Docker 镜像</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">实例状态</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="config in mcpStore.configurations" :key="config.id">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ config.name }}</div>
                <div class="text-sm text-gray-500">{{ config.description }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ config.dockerImage }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="config.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
                      class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ config.enabled ? '已启用' : '已禁用' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span v-if="getInstanceForConfig(config.id!)"
                      :class="getStatusColor(getInstanceForConfig(config.id!)?.status)"
                      class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ translateStatus(getInstanceForConfig(config.id!)?.status) }}
                </span>
                <span v-else class="text-sm text-gray-500">未运行</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button
                  v-if="!getInstanceForConfig(config.id!)"
                  @click="startServer(config.id!)"
                  :disabled="!config.enabled || mcpStore.loading"
                  class="text-green-600 hover:text-green-900 disabled:text-gray-400"
                >
                  启动
                </button>
                <button
                  v-else
                  @click="stopServer(getInstanceForConfig(config.id!)!.sandboxId)"
                  :disabled="mcpStore.loading"
                  class="text-red-600 hover:text-red-900 disabled:text-gray-400"
                >
                  停止
                </button>
                <button
                  @click="editConfiguration(config)"
                  class="text-blue-600 hover:text-blue-900"
                >
                  编辑
                </button>
                <button
                  @click="toggleConfiguration(config.id!)"
                  :disabled="mcpStore.loading"
                  class="text-yellow-600 hover:text-yellow-900 disabled:text-gray-400"
                >
                  {{ config.enabled ? '禁用' : '启用' }}
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Create/Edit Modal -->
    <McpConfigurationModal
      v-if="showCreateModal || editingConfig"
      :config="editingConfig"
      @close="closeModal"
      @save="handleSave"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useMcpStore } from '@/store/mcp'
import { useAuthStore } from '@/store/auth'
import type { McpServerConfiguration, McpServerInstance } from '@/types/mcp'
import McpConfigurationModal from '@/components/McpConfigurationModal.vue'

const router = useRouter()
const mcpStore = useMcpStore()
const authStore = useAuthStore()
const showCreateModal = ref(false)
const editingConfig = ref<McpServerConfiguration | null>(null)

const currentUser = computed(() => {
  return authStore.user?.username || '用户'
})

onMounted(async () => {
  await Promise.all([
    mcpStore.fetchConfigurations(),
    mcpStore.fetchInstances()
  ])
})

const getInstanceForConfig = (configId: number): McpServerInstance | undefined => {
  return mcpStore.instances.find(instance =>
    instance.configurationId === configId &&
    (instance.status === 'RUNNING' || instance.status === 'STARTING')
  )
}

const getStatusColor = (status?: string) => {
  switch (status) {
    case 'RUNNING': return 'bg-green-100 text-green-800'
    case 'STARTING': return 'bg-yellow-100 text-yellow-800'
    case 'STOPPING': return 'bg-orange-100 text-orange-800'
    case 'FAILED': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const translateStatus = (status?: string) => {
  switch (status) {
    case 'CREATING': return '创建中'
    case 'CREATED': return '已创建'
    case 'STARTING': return '启动中'
    case 'RUNNING': return '运行中'
    case 'STOPPING': return '停止中'
    case 'STOPPED': return '已停止'
    case 'FAILED': return '失败'
    case 'DESTROYING': return '销毁中'
    case 'DESTROYED': return '已销毁'
    default: return status || '未知'
  }
}

const startServer = async (configId: number) => {
  try {
    await mcpStore.startServer(configId)
  } catch (error) {
    console.error('Failed to start server:', error)
  }
}

const stopServer = async (sandboxId: string) => {
  try {
    await mcpStore.stopServer(sandboxId)
  } catch (error) {
    console.error('Failed to stop server:', error)
  }
}

const editConfiguration = (config: McpServerConfiguration) => {
  editingConfig.value = { ...config }
}

const toggleConfiguration = async (configId: number) => {
  try {
    await mcpStore.toggleConfiguration(configId)
  } catch (error) {
    console.error('Failed to toggle configuration:', error)
  }
}

const closeModal = () => {
  showCreateModal.value = false
  editingConfig.value = null
}

const handleSave = async (config: McpServerConfiguration) => {
  try {
    if (editingConfig.value) {
      await mcpStore.updateConfiguration(editingConfig.value.id!, config)
    } else {
      await mcpStore.createConfiguration(config)
    }
    closeModal()
  } catch (error) {
    console.error('Failed to save configuration:', error)
  }
}

const logout = async () => {
  try {
    await authStore.logout()
    router.push('/login')
  } catch (error) {
    console.error('Failed to logout:', error)
  }
}
</script>

<style scoped>
.mcp-dashboard {
  @apply p-6 max-w-7xl mx-auto;
}

.dashboard-header {
  @apply flex justify-between items-center mb-8;
}
</style>
