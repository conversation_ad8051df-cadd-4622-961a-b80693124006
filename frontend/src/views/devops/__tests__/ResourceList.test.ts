/**
 * ResourceList组件的单元测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import ResourceList from '../ResourceList.vue'
import { RESOURCE_TYPES } from '@/types/devops'

// Mock组件
vi.mock('@/components/common/DataTable.vue', () => ({
  default: {
    name: 'DataTable',
    template: '<div data-testid="data-table"><slot name="actions"></slot><slot name="cell-status"></slot><slot name="actions" :record="{}"></slot></div>',
    props: ['data', 'columns', 'loading', 'title', 'searchable', 'search-placeholder'],
    emits: ['search', 'row-click']
  }
}))

vi.mock('@/components/common/StatusTag.vue', () => ({
  default: {
    name: 'StatusTag',
    template: '<span data-testid="status-tag">{{ status }}</span>',
    props: ['status']
  }
}))

vi.mock('@/components/common/Breadcrumb.vue', () => ({
  default: {
    name: 'Breadcrumb',
    template: '<nav data-testid="breadcrumb"></nav>',
    props: ['items']
  }
}))

vi.mock('@/components/common/FormField.vue', () => ({
  default: {
    name: 'FormField',
    template: '<div data-testid="form-field"><input :value="modelValue" @input="$emit(\'update:modelValue\', $event.target.value)" /></div>',
    props: ['modelValue', 'label', 'type', 'placeholder', 'required', 'error-message', 'options', 'rows'],
    emits: ['update:modelValue']
  }
}))

vi.mock('@/components/common/ConfirmDialog.vue', () => ({
  default: {
    name: 'ConfirmDialog',
    template: '<div data-testid="confirm-dialog" v-if="visible"></div>',
    props: ['visible', 'title', 'message', 'type', 'confirm-text', 'loading'],
    emits: ['confirm', 'cancel']
  }
}))

// Mock store
vi.mock('@/store/devops', () => ({
  useDevOpsStore: () => ({
    resources: [],
    components: [],
    loading: { resources: false },
    loadResources: vi.fn(),
    loadComponents: vi.fn(),
    createResource: vi.fn(),
    updateResource: vi.fn(),
    deleteResource: vi.fn()
  })
}))

const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', component: { template: '<div>Home</div>' } },
    { path: '/devops/resources/:id', component: { template: '<div>Resource Detail</div>' } },
    { path: '/devops/components/:id', component: { template: '<div>Component Detail</div>' } }
  ]
})

describe('ResourceList', () => {
  let wrapper: any
  let pinia: any

  beforeEach(() => {
    pinia = createPinia()
    wrapper = mount(ResourceList, {
      global: {
        plugins: [router, pinia]
      }
    })
  })

  it('renders correctly', () => {
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('[data-testid="breadcrumb"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="data-table"]').exists()).toBe(true)
  })

  it('displays page header with title and description', () => {
    const header = wrapper.find('.page-header')
    expect(header.exists()).toBe(true)
    expect(header.text()).toContain('资源管理')
    expect(header.text()).toContain('管理组件相关的资源，包括数据库、缓存、存储等基础设施资源')
  })

  it('shows create resource button', () => {
    const createButton = wrapper.find('.btn-primary')
    expect(createButton.exists()).toBe(true)
    expect(createButton.text()).toContain('创建资源')
  })

  it('displays filter section with component, resource type and status filters', () => {
    const filtersSection = wrapper.find('.filters-section')
    expect(filtersSection.exists()).toBe(true)
    
    const filterSelects = wrapper.findAll('.filter-select')
    expect(filterSelects).toHaveLength(3)
  })

  it('opens create modal when create button is clicked', async () => {
    const createButton = wrapper.find('.btn-primary')
    await createButton.trigger('click')
    
    expect(wrapper.vm.showCreateModal).toBe(true)
    expect(wrapper.find('.modal-overlay').exists()).toBe(true)
  })

  it('displays modal with correct title when creating', async () => {
    wrapper.vm.showCreateModal = true
    await wrapper.vm.$nextTick()
    
    const modalHeader = wrapper.find('.modal-header h3')
    expect(modalHeader.text()).toBe('创建资源')
  })

  it('displays modal with correct title when editing', async () => {
    wrapper.vm.showEditModal = true
    await wrapper.vm.$nextTick()
    
    const modalHeader = wrapper.find('.modal-header h3')
    expect(modalHeader.text()).toBe('编辑资源')
  })

  it('validates form before submission', async () => {
    wrapper.vm.showCreateModal = true
    await wrapper.vm.$nextTick()
    
    // 尝试提交空表单
    const form = wrapper.find('.modal-form')
    await form.trigger('submit')
    
    expect(wrapper.vm.formErrors.name).toBeTruthy()
    expect(wrapper.vm.formErrors.componentId).toBeTruthy()
    expect(wrapper.vm.formErrors.type).toBeTruthy()
  })

  it('filters resources by component', async () => {
    // 设置测试数据
    wrapper.vm.resources = [
      { id: 1, name: '资源1', componentId: 1, type: 'SERVICE', status: 'ACTIVE' },
      { id: 2, name: '资源2', componentId: 2, type: 'DATABASE', status: 'ACTIVE' }
    ]
    
    wrapper.vm.selectedComponentId = 1
    await wrapper.vm.$nextTick()
    
    const filtered = wrapper.vm.filteredResources
    expect(filtered).toHaveLength(1)
    expect(filtered[0].componentId).toBe(1)
  })

  it('filters resources by type', async () => {
    // 设置测试数据
    wrapper.vm.resources = [
      { id: 1, name: '资源1', componentId: 1, type: 'SERVICE', status: 'ACTIVE' },
      { id: 2, name: '资源2', componentId: 1, type: 'DATABASE', status: 'ACTIVE' }
    ]
    
    wrapper.vm.selectedResourceType = 'SERVICE'
    await wrapper.vm.$nextTick()
    
    const filtered = wrapper.vm.filteredResources
    expect(filtered).toHaveLength(1)
    expect(filtered[0].type).toBe('SERVICE')
  })

  it('filters resources by status', async () => {
    // 设置测试数据
    wrapper.vm.resources = [
      { id: 1, name: '资源1', componentId: 1, type: 'SERVICE', status: 'ACTIVE' },
      { id: 2, name: '资源2', componentId: 1, type: 'SERVICE', status: 'INACTIVE' }
    ]
    
    wrapper.vm.selectedStatus = 'ACTIVE'
    await wrapper.vm.$nextTick()
    
    const filtered = wrapper.vm.filteredResources
    expect(filtered).toHaveLength(1)
    expect(filtered[0].status).toBe('ACTIVE')
  })

  it('gets component name correctly', () => {
    wrapper.vm.components = [
      { id: 1, name: '测试组件', applicationId: 1, status: 'ACTIVE' }
    ]
    
    const componentName = wrapper.vm.getComponentName(1)
    expect(componentName).toBe('测试组件')
    
    const unknownComponentName = wrapper.vm.getComponentName(999)
    expect(unknownComponentName).toBe('未知组件')
  })

  it('gets resource type label correctly', () => {
    const serviceLabel = wrapper.vm.getResourceTypeLabel(RESOURCE_TYPES.SERVICE)
    expect(serviceLabel).toBe('服务')
    
    const databaseLabel = wrapper.vm.getResourceTypeLabel(RESOURCE_TYPES.DATABASE)
    expect(databaseLabel).toBe('数据库')
  })

  it('gets path placeholder correctly', () => {
    const servicePlaceholder = wrapper.vm.getPathPlaceholder(RESOURCE_TYPES.SERVICE)
    expect(servicePlaceholder).toBe('/api/service')
    
    const databasePlaceholder = wrapper.vm.getPathPlaceholder(RESOURCE_TYPES.DATABASE)
    expect(databasePlaceholder).toBe('******************************')
  })

  it('checks if connection can be tested', () => {
    const canTestService = wrapper.vm.canTestConnection(RESOURCE_TYPES.SERVICE)
    expect(canTestService).toBe(true)
    
    const canTestStorage = wrapper.vm.canTestConnection(RESOURCE_TYPES.STORAGE)
    expect(canTestStorage).toBe(false)
  })

  it('gets config fields correctly', () => {
    const serviceFields = wrapper.vm.getConfigFields(RESOURCE_TYPES.SERVICE)
    expect(serviceFields).toHaveLength(3)
    expect(serviceFields[0].key).toBe('host')
    
    const databaseFields = wrapper.vm.getConfigFields(RESOURCE_TYPES.DATABASE)
    expect(databaseFields).toHaveLength(5)
    expect(databaseFields[0].key).toBe('host')
  })

  it('handles resource type change', () => {
    wrapper.vm.formData.type = RESOURCE_TYPES.SERVICE
    wrapper.vm.handleResourceTypeChange()
    
    expect(wrapper.vm.formData.configuration).toEqual({})
    expect(wrapper.vm.formData.path).toBe('/api/service')
  })

  it('handles navigation to component detail', () => {
    const pushSpy = vi.spyOn(router, 'push')
    
    wrapper.vm.viewComponent(1)
    
    expect(pushSpy).toHaveBeenCalledWith('/devops/components/1')
  })

  it('handles navigation to resource detail', () => {
    const pushSpy = vi.spyOn(router, 'push')
    const testResource = { id: 1, name: '测试资源', componentId: 1, type: 'SERVICE', status: 'ACTIVE' }
    
    wrapper.vm.viewResource(testResource)
    
    expect(pushSpy).toHaveBeenCalledWith('/devops/resources/1')
  })

  it('shows delete confirmation dialog', async () => {
    const testResource = { id: 1, name: '测试资源', componentId: 1, type: 'SERVICE', status: 'ACTIVE' }
    wrapper.vm.deleteResource(testResource)
    
    expect(wrapper.vm.showDeleteDialog).toBe(true)
    expect(wrapper.vm.deleteTarget).toEqual(testResource)
  })

  it('resets form when closing modal', async () => {
    // 设置表单数据
    wrapper.vm.formData.name = '测试资源'
    wrapper.vm.formData.description = '测试描述'
    wrapper.vm.formData.configuration = { host: 'localhost' }
    wrapper.vm.formErrors.name = '错误信息'
    
    wrapper.vm.closeModal()
    
    expect(wrapper.vm.formData.name).toBe('')
    expect(wrapper.vm.formData.description).toBe('')
    expect(wrapper.vm.formData.configuration).toEqual({})
    expect(wrapper.vm.formErrors.name).toBe('')
  })

  it('computes component options correctly', () => {
    wrapper.vm.components = [
      { id: 1, name: '组件1', applicationId: 1, status: 'ACTIVE' },
      { id: 2, name: '组件2', applicationId: 1, status: 'ACTIVE' }
    ]
    
    const options = wrapper.vm.componentOptions
    expect(options).toHaveLength(2)
    expect(options[0]).toEqual({ label: '组件1', value: 1 })
    expect(options[1]).toEqual({ label: '组件2', value: 2 })
  })

  it('computes resource type options correctly', () => {
    wrapper.vm.resourceTypes = [RESOURCE_TYPES.SERVICE, RESOURCE_TYPES.DATABASE]
    
    const options = wrapper.vm.resourceTypeOptions
    expect(options).toHaveLength(2)
    expect(options[0]).toEqual({ label: '服务', value: RESOURCE_TYPES.SERVICE })
    expect(options[1]).toEqual({ label: '数据库', value: RESOURCE_TYPES.DATABASE })
  })

  it('handles search functionality', async () => {
    const searchQuery = '数据库'
    wrapper.vm.handleSearch(searchQuery)
    
    expect(wrapper.vm.searchQuery).toBe(searchQuery)
  })

  it('formats date correctly', () => {
    const dateString = '2024-01-15T10:30:00Z'
    const formatted = wrapper.vm.formatDate(dateString)
    
    expect(formatted).toContain('2024')
    expect(typeof formatted).toBe('string')
  })

  it('handles connection test', async () => {
    const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {})
    const testResource = {
      id: 1,
      name: '测试资源',
      componentId: 1,
      type: RESOURCE_TYPES.SERVICE,
      status: 'ACTIVE'
    }
    
    await wrapper.vm.testConnection(testResource)
    
    expect(alertSpy).toHaveBeenCalled()
    alertSpy.mockRestore()
  })
})
