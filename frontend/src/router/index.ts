import { createRouter, create<PERSON>ebHistory, RouteRecordRaw, NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import LoginView from '../views/LoginView.vue'
import HomeView from '../views/HomeView.vue'
import McpDashboard from '../views/McpDashboard.vue'
import { useAuthStore } from '../store/auth'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect: '/mcp'
  },
  {
    path: '/home',
    name: 'home',
    component: HomeView,
    meta: { requiresAuth: true }
  },
  {
    path: '/mcp',
    name: 'mcp-dashboard',
    component: McpDashboard,
    meta: { requiresAuth: true }
  },
  {
    path: '/devops',
    name: 'devops-dashboard',
    component: () => import('../views/DevOpsDashboard.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/devops/projects',
    name: 'devops-projects',
    component: () => import('../views/devops/ProjectList.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/devops/applications',
    name: 'devops-applications',
    component: () => import('../views/devops/ApplicationList.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/devops/components',
    name: 'devops-components',
    component: () => import('../views/devops/ComponentList.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/devops/resources',
    name: 'devops-resources',
    component: () => import('../views/devops/ResourceList.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/devops/ci-cd',
    name: 'devops-cicd',
    component: () => import('../views/devops/CICDManagement.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/devops/monitoring',
    name: 'devops-monitoring',
    component: () => import('../views/devops/MonitoringCenter.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/login',
    name: 'login',
    component: LoginView
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

router.beforeEach((
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) => {
  const authStore = useAuthStore()

  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } else {
    next()
  }
})

export default router
