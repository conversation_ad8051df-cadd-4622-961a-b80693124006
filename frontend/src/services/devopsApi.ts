/**
 * DevOps管理模块的API服务层
 * 封装所有与DevOps相关的API调用
 */

import type {
  DevOpsProject,
  DevOpsApplication,
  DevOpsComponent,
  DevOpsResource,
  DevOpsCiTask,
  DevOpsCdTask,
  DevOpsDeploymentTemplate,
  DevOpsTektonCluster,
  ApiResponse,
  PagedResponse,
  QueryParams
} from '@/types/devops'

// API基础配置
const API_BASE_URL = '/api/devops'

// 通用请求函数
async function request<T>(
  url: string,
  options: RequestInit = {}
): Promise<T> {
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  })

  if (!response.ok) {
    const error = await response.text()
    throw new Error(error || `HTTP ${response.status}`)
  }

  return response.json()
}

// 项目管理API
export const projectApi = {
  // 获取所有项目
  async getAll(params?: QueryParams): Promise<DevOpsProject[]> {
    const queryString = params ? new URLSearchParams(params as any).toString() : ''
    const url = `${API_BASE_URL}/projects${queryString ? `?${queryString}` : ''}`
    return request<DevOpsProject[]>(url)
  },

  // 根据ID获取项目
  async getById(id: number): Promise<DevOpsProject> {
    return request<DevOpsProject>(`${API_BASE_URL}/projects/${id}`)
  },

  // 创建项目
  async create(project: Omit<DevOpsProject, 'id' | 'createdAt' | 'updatedAt'>): Promise<DevOpsProject> {
    return request<DevOpsProject>(`${API_BASE_URL}/projects`, {
      method: 'POST',
      body: JSON.stringify(project)
    })
  },

  // 更新项目
  async update(id: number, project: Partial<DevOpsProject>): Promise<DevOpsProject> {
    return request<DevOpsProject>(`${API_BASE_URL}/projects/${id}`, {
      method: 'PUT',
      body: JSON.stringify(project)
    })
  },

  // 删除项目
  async delete(id: number): Promise<void> {
    return request<void>(`${API_BASE_URL}/projects/${id}`, {
      method: 'DELETE'
    })
  },

  // 搜索项目
  async search(query: string): Promise<DevOpsProject[]> {
    return request<DevOpsProject[]>(`${API_BASE_URL}/projects/search?q=${encodeURIComponent(query)}`)
  },

  // 获取项目统计
  async getStats(): Promise<{ total: number; active: number; inactive: number }> {
    return request<{ total: number; active: number; inactive: number }>(`${API_BASE_URL}/projects/stats`)
  }
}

// 应用管理API
export const applicationApi = {
  // 获取所有应用
  async getAll(params?: QueryParams): Promise<DevOpsApplication[]> {
    const queryString = params ? new URLSearchParams(params as any).toString() : ''
    const url = `${API_BASE_URL}/applications${queryString ? `?${queryString}` : ''}`
    return request<DevOpsApplication[]>(url)
  },

  // 根据项目ID获取应用
  async getByProject(projectId: number): Promise<DevOpsApplication[]> {
    return request<DevOpsApplication[]>(`${API_BASE_URL}/projects/${projectId}/applications`)
  },

  // 根据ID获取应用
  async getById(id: number): Promise<DevOpsApplication> {
    return request<DevOpsApplication>(`${API_BASE_URL}/applications/${id}`)
  },

  // 创建应用
  async create(application: Omit<DevOpsApplication, 'id' | 'createdAt' | 'updatedAt'>): Promise<DevOpsApplication> {
    return request<DevOpsApplication>(`${API_BASE_URL}/applications`, {
      method: 'POST',
      body: JSON.stringify(application)
    })
  },

  // 更新应用
  async update(id: number, application: Partial<DevOpsApplication>): Promise<DevOpsApplication> {
    return request<DevOpsApplication>(`${API_BASE_URL}/applications/${id}`, {
      method: 'PUT',
      body: JSON.stringify(application)
    })
  },

  // 删除应用
  async delete(id: number): Promise<void> {
    return request<void>(`${API_BASE_URL}/applications/${id}`, {
      method: 'DELETE'
    })
  },

  // 搜索应用
  async search(query: string): Promise<DevOpsApplication[]> {
    return request<DevOpsApplication[]>(`${API_BASE_URL}/applications/search?q=${encodeURIComponent(query)}`)
  }
}

// 组件管理API
export const componentApi = {
  // 获取所有组件
  async getAll(params?: QueryParams): Promise<DevOpsComponent[]> {
    const queryString = params ? new URLSearchParams(params as any).toString() : ''
    const url = `${API_BASE_URL}/components${queryString ? `?${queryString}` : ''}`
    return request<DevOpsComponent[]>(url)
  },

  // 根据应用ID获取组件
  async getByApplication(applicationId: number): Promise<DevOpsComponent[]> {
    return request<DevOpsComponent[]>(`${API_BASE_URL}/applications/${applicationId}/components`)
  },

  // 根据ID获取组件
  async getById(id: number): Promise<DevOpsComponent> {
    return request<DevOpsComponent>(`${API_BASE_URL}/components/${id}`)
  },

  // 创建组件
  async create(component: Omit<DevOpsComponent, 'id' | 'createdAt' | 'updatedAt'>): Promise<DevOpsComponent> {
    return request<DevOpsComponent>(`${API_BASE_URL}/components`, {
      method: 'POST',
      body: JSON.stringify(component)
    })
  },

  // 更新组件
  async update(id: number, component: Partial<DevOpsComponent>): Promise<DevOpsComponent> {
    return request<DevOpsComponent>(`${API_BASE_URL}/components/${id}`, {
      method: 'PUT',
      body: JSON.stringify(component)
    })
  },

  // 删除组件
  async delete(id: number): Promise<void> {
    return request<void>(`${API_BASE_URL}/components/${id}`, {
      method: 'DELETE'
    })
  },

  // 搜索组件
  async search(query: string): Promise<DevOpsComponent[]> {
    return request<DevOpsComponent[]>(`${API_BASE_URL}/components/search?q=${encodeURIComponent(query)}`)
  }
}

// 资源管理API
export const resourceApi = {
  // 获取所有资源
  async getAll(params?: QueryParams): Promise<DevOpsResource[]> {
    const queryString = params ? new URLSearchParams(params as any).toString() : ''
    const url = `${API_BASE_URL}/resources${queryString ? `?${queryString}` : ''}`
    return request<DevOpsResource[]>(url)
  },

  // 根据组件ID获取资源
  async getByComponent(componentId: number): Promise<DevOpsResource[]> {
    return request<DevOpsResource[]>(`${API_BASE_URL}/components/${componentId}/resources`)
  },

  // 根据ID获取资源
  async getById(id: number): Promise<DevOpsResource> {
    return request<DevOpsResource>(`${API_BASE_URL}/resources/${id}`)
  },

  // 创建资源
  async create(resource: Omit<DevOpsResource, 'id' | 'createdAt' | 'updatedAt'>): Promise<DevOpsResource> {
    return request<DevOpsResource>(`${API_BASE_URL}/resources`, {
      method: 'POST',
      body: JSON.stringify(resource)
    })
  },

  // 更新资源
  async update(id: number, resource: Partial<DevOpsResource>): Promise<DevOpsResource> {
    return request<DevOpsResource>(`${API_BASE_URL}/resources/${id}`, {
      method: 'PUT',
      body: JSON.stringify(resource)
    })
  },

  // 删除资源
  async delete(id: number): Promise<void> {
    return request<void>(`${API_BASE_URL}/resources/${id}`, {
      method: 'DELETE'
    })
  },

  // 搜索资源
  async search(query: string): Promise<DevOpsResource[]> {
    return request<DevOpsResource[]>(`${API_BASE_URL}/resources/search?q=${encodeURIComponent(query)}`)
  },

  // 获取资源类型
  async getTypes(): Promise<string[]> {
    return request<string[]>(`${API_BASE_URL}/resources/types`)
  }
}

// CI任务管理API
export const ciTaskApi = {
  // 获取所有CI任务
  async getAll(params?: QueryParams): Promise<DevOpsCiTask[]> {
    const queryString = params ? new URLSearchParams(params as any).toString() : ''
    const url = `${API_BASE_URL}/ci/tasks${queryString ? `?${queryString}` : ''}`
    return request<DevOpsCiTask[]>(url)
  },

  // 根据组件ID获取CI任务
  async getByComponent(componentId: number): Promise<DevOpsCiTask[]> {
    return request<DevOpsCiTask[]>(`${API_BASE_URL}/components/${componentId}/ci/tasks`)
  },

  // 根据ID获取CI任务
  async getById(id: number): Promise<DevOpsCiTask> {
    return request<DevOpsCiTask>(`${API_BASE_URL}/ci/tasks/${id}`)
  },

  // 创建CI任务
  async create(task: Omit<DevOpsCiTask, 'id' | 'createdAt' | 'updatedAt'>): Promise<DevOpsCiTask> {
    return request<DevOpsCiTask>(`${API_BASE_URL}/ci/tasks`, {
      method: 'POST',
      body: JSON.stringify(task)
    })
  },

  // 更新CI任务
  async update(id: number, task: Partial<DevOpsCiTask>): Promise<DevOpsCiTask> {
    return request<DevOpsCiTask>(`${API_BASE_URL}/ci/tasks/${id}`, {
      method: 'PUT',
      body: JSON.stringify(task)
    })
  },

  // 删除CI任务
  async delete(id: number): Promise<void> {
    return request<void>(`${API_BASE_URL}/ci/tasks/${id}`, {
      method: 'DELETE'
    })
  },

  // 启动CI任务
  async start(id: number, parameters?: Record<string, any>): Promise<{ instanceId: string }> {
    return request<{ instanceId: string }>(`${API_BASE_URL}/ci/tasks/${id}/start`, {
      method: 'POST',
      body: JSON.stringify(parameters || {})
    })
  },

  // 停止CI任务
  async stop(id: number): Promise<void> {
    return request<void>(`${API_BASE_URL}/ci/tasks/${id}/stop`, {
      method: 'POST'
    })
  },

  // 获取支持的任务类型
  async getTypes(): Promise<string[]> {
    return request<string[]>(`${API_BASE_URL}/ci/tasks/types`)
  }
}

// CD任务管理API
export const cdTaskApi = {
  // 获取所有CD任务
  async getAll(params?: QueryParams): Promise<DevOpsCdTask[]> {
    const queryString = params ? new URLSearchParams(params as any).toString() : ''
    const url = `${API_BASE_URL}/cd/tasks${queryString ? `?${queryString}` : ''}`
    return request<DevOpsCdTask[]>(url)
  },

  // 根据应用ID获取CD任务
  async getByApplication(applicationId: number): Promise<DevOpsCdTask[]> {
    return request<DevOpsCdTask[]>(`${API_BASE_URL}/applications/${applicationId}/cd/tasks`)
  },

  // 根据ID获取CD任务
  async getById(id: number): Promise<DevOpsCdTask> {
    return request<DevOpsCdTask>(`${API_BASE_URL}/cd/tasks/${id}`)
  },

  // 创建CD任务
  async create(task: Omit<DevOpsCdTask, 'id' | 'createdAt' | 'updatedAt'>): Promise<DevOpsCdTask> {
    return request<DevOpsCdTask>(`${API_BASE_URL}/cd/tasks`, {
      method: 'POST',
      body: JSON.stringify(task)
    })
  },

  // 更新CD任务
  async update(id: number, task: Partial<DevOpsCdTask>): Promise<DevOpsCdTask> {
    return request<DevOpsCdTask>(`${API_BASE_URL}/cd/tasks/${id}`, {
      method: 'PUT',
      body: JSON.stringify(task)
    })
  },

  // 删除CD任务
  async delete(id: number): Promise<void> {
    return request<void>(`${API_BASE_URL}/cd/tasks/${id}`, {
      method: 'DELETE'
    })
  },

  // 启动CD任务
  async start(id: number, parameters?: Record<string, any>): Promise<{ instanceId: string }> {
    return request<{ instanceId: string }>(`${API_BASE_URL}/cd/tasks/${id}/start`, {
      method: 'POST',
      body: JSON.stringify(parameters || {})
    })
  },

  // 停止CD任务
  async stop(id: number): Promise<void> {
    return request<void>(`${API_BASE_URL}/cd/tasks/${id}/stop`, {
      method: 'POST'
    })
  }
}

// 统计API
export const statsApi = {
  // 获取总体统计
  async getOverview(): Promise<{
    projects: number
    applications: number
    components: number
    resources: number
  }> {
    return request<{
      projects: number
      applications: number
      components: number
      resources: number
    }>(`${API_BASE_URL}/stats/overview`)
  },

  // 获取任务状态统计
  async getTaskStats(): Promise<{
    ci: { total: number; running: number; completed: number; failed: number }
    cd: { total: number; running: number; completed: number; failed: number }
  }> {
    return request<{
      ci: { total: number; running: number; completed: number; failed: number }
      cd: { total: number; running: number; completed: number; failed: number }
    }>(`${API_BASE_URL}/stats/tasks`)
  }
}
