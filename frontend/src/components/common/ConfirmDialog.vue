<template>
  <teleport to="body">
    <div
      v-if="visible"
      class="dialog-overlay"
      @click="handleOverlayClick"
    >
      <div
        class="dialog-container"
        :class="sizeClasses"
        @click.stop
      >
        <!-- 对话框头部 -->
        <div class="dialog-header">
          <div class="dialog-title-section">
            <div class="dialog-icon" :class="iconClasses">
              <svg class="icon" viewBox="0 0 20 20" fill="currentColor">
                <path v-if="type === 'warning'" fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                <path v-else-if="type === 'danger'" fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                <path v-else-if="type === 'info'" fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                <path v-else fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
              </svg>
            </div>
            <h3 class="dialog-title">{{ title }}</h3>
          </div>
          <button
            v-if="closable"
            @click="handleCancel"
            class="dialog-close"
            type="button"
          >
            <svg class="close-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>

        <!-- 对话框内容 -->
        <div class="dialog-content">
          <p v-if="message" class="dialog-message">{{ message }}</p>
          <slot v-else></slot>
          
          <!-- 详细信息 -->
          <div v-if="details" class="dialog-details">
            <details class="details-disclosure">
              <summary class="details-summary">查看详细信息</summary>
              <div class="details-content">
                <pre v-if="typeof details === 'string'">{{ details }}</pre>
                <div v-else>
                  <div v-for="(value, key) in details" :key="key" class="detail-item">
                    <span class="detail-key">{{ key }}:</span>
                    <span class="detail-value">{{ value }}</span>
                  </div>
                </div>
              </div>
            </details>
          </div>
        </div>

        <!-- 对话框底部 -->
        <div class="dialog-footer">
          <button
            v-if="showCancel"
            @click="handleCancel"
            :disabled="loading"
            class="dialog-button dialog-button-cancel"
            type="button"
          >
            {{ cancelText }}
          </button>
          <button
            @click="handleConfirm"
            :disabled="loading"
            :class="confirmButtonClasses"
            class="dialog-button"
            type="button"
          >
            <svg v-if="loading" class="loading-spinner" viewBox="0 0 20 20">
              <path d="M10 3a7 7 0 100 14 7 7 0 000-14zM2 10a8 8 0 1116 0 8 8 0 01-16 0z" fill="currentColor" opacity="0.3"/>
              <path d="M10 2a8 8 0 018 8" fill="currentColor">
                <animateTransform
                  attributeName="transform"
                  type="rotate"
                  from="0 10 10"
                  to="360 10 10"
                  dur="1s"
                  repeatCount="indefinite"
                />
              </path>
            </svg>
            <span>{{ loading ? loadingText : confirmText }}</span>
          </button>
        </div>
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, onUnmounted, watch } from 'vue'

interface Props {
  visible: boolean
  title: string
  message?: string
  details?: string | Record<string, any>
  type?: 'info' | 'warning' | 'danger' | 'question'
  size?: 'small' | 'medium' | 'large'
  confirmText?: string
  cancelText?: string
  loadingText?: string
  showCancel?: boolean
  closable?: boolean
  loading?: boolean
  maskClosable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'question',
  size: 'medium',
  confirmText: '确认',
  cancelText: '取消',
  loadingText: '处理中...',
  showCancel: true,
  closable: true,
  loading: false,
  maskClosable: true
})

const emit = defineEmits<{
  confirm: []
  cancel: []
  close: []
}>()

const sizeClasses = computed(() => {
  const sizes = {
    small: 'max-w-sm',
    medium: 'max-w-md',
    large: 'max-w-lg'
  }
  return sizes[props.size]
})

const iconClasses = computed(() => {
  const types = {
    info: 'text-blue-500 bg-blue-100',
    warning: 'text-yellow-500 bg-yellow-100',
    danger: 'text-red-500 bg-red-100',
    question: 'text-gray-500 bg-gray-100'
  }
  return types[props.type]
})

const confirmButtonClasses = computed(() => {
  const types = {
    info: 'dialog-button-primary',
    warning: 'dialog-button-warning',
    danger: 'dialog-button-danger',
    question: 'dialog-button-primary'
  }
  return types[props.type]
})

const handleConfirm = () => {
  if (!props.loading) {
    emit('confirm')
  }
}

const handleCancel = () => {
  if (!props.loading) {
    emit('cancel')
    emit('close')
  }
}

const handleOverlayClick = () => {
  if (props.maskClosable && !props.loading) {
    handleCancel()
  }
}

const handleEscapeKey = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.visible && props.closable && !props.loading) {
    handleCancel()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleEscapeKey)
  // 防止背景滚动
  if (props.visible) {
    document.body.style.overflow = 'hidden'
  }
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleEscapeKey)
  document.body.style.overflow = ''
})

// 监听visible变化，控制背景滚动
watch(() => props.visible, (newVisible) => {
  nextTick(() => {
    document.body.style.overflow = newVisible ? 'hidden' : ''
  })
})
</script>

<style scoped>
.dialog-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50;
  animation: fadeIn 0.2s ease-out;
}

.dialog-container {
  @apply bg-white rounded-lg shadow-xl w-full transform;
  animation: slideIn 0.2s ease-out;
}

.dialog-header {
  @apply flex items-center justify-between p-6 pb-4;
}

.dialog-title-section {
  @apply flex items-center gap-3;
}

.dialog-icon {
  @apply w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0;
}

.icon {
  @apply w-6 h-6;
}

.dialog-title {
  @apply text-lg font-semibold text-gray-900 m-0;
}

.dialog-close {
  @apply text-gray-400 hover:text-gray-600 transition-colors p-1 rounded;
}

.close-icon {
  @apply w-5 h-5;
}

.dialog-content {
  @apply px-6 pb-4;
}

.dialog-message {
  @apply text-gray-700 leading-relaxed m-0;
}

.dialog-details {
  @apply mt-4;
}

.details-disclosure {
  @apply border border-gray-200 rounded;
}

.details-summary {
  @apply px-3 py-2 bg-gray-50 cursor-pointer text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors;
}

.details-content {
  @apply p-3 border-t border-gray-200 bg-white;
}

.details-content pre {
  @apply text-xs text-gray-600 whitespace-pre-wrap font-mono bg-gray-50 p-2 rounded;
}

.detail-item {
  @apply flex gap-2 text-sm py-1;
}

.detail-key {
  @apply font-medium text-gray-700 min-w-20;
}

.detail-value {
  @apply text-gray-600;
}

.dialog-footer {
  @apply flex items-center justify-end gap-3 px-6 py-4 bg-gray-50 rounded-b-lg;
}

.dialog-button {
  @apply px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2;
}

.dialog-button-cancel {
  @apply text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:ring-gray-500;
}

.dialog-button-primary {
  @apply text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500;
}

.dialog-button-warning {
  @apply text-white bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500;
}

.dialog-button-danger {
  @apply text-white bg-red-600 hover:bg-red-700 focus:ring-red-500;
}

.loading-spinner {
  @apply w-4 h-4 animate-spin;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>
