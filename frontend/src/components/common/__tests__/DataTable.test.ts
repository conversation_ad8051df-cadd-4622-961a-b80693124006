/**
 * DataTable组件的单元测试
 */

import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import DataTable from '../DataTable.vue'
import type { TableColumn } from '@/types/devops'

const mockData = [
  { id: 1, name: '项目A', status: 'ACTIVE', createdAt: '2024-01-01' },
  { id: 2, name: '项目B', status: 'INACTIVE', createdAt: '2024-01-02' },
  { id: 3, name: '项目C', status: 'ACTIVE', createdAt: '2024-01-03' }
]

const mockColumns: TableColumn[] = [
  { key: 'name', title: '名称', sortable: true },
  { key: 'status', title: '状态' },
  { key: 'createdAt', title: '创建时间', sortable: true }
]

describe('DataTable', () => {
  it('renders table with data', () => {
    const wrapper = mount(DataTable, {
      props: {
        data: mockData,
        columns: mockColumns
      }
    })
    
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('table').exists()).toBe(true)
    expect(wrapper.findAll('tbody tr')).toHaveLength(3)
  })

  it('displays column headers correctly', () => {
    const wrapper = mount(DataTable, {
      props: {
        data: mockData,
        columns: mockColumns
      }
    })
    
    const headers = wrapper.findAll('th')
    expect(headers[0].text()).toContain('名称')
    expect(headers[1].text()).toContain('状态')
    expect(headers[2].text()).toContain('创建时间')
  })

  it('displays data in table cells', () => {
    const wrapper = mount(DataTable, {
      props: {
        data: mockData,
        columns: mockColumns
      }
    })
    
    const firstRow = wrapper.findAll('tbody tr')[0]
    const cells = firstRow.findAll('td')
    
    expect(cells[0].text()).toContain('项目A')
    expect(cells[1].text()).toContain('ACTIVE')
    expect(cells[2].text()).toContain('2024-01-01')
  })

  it('shows loading state', () => {
    const wrapper = mount(DataTable, {
      props: {
        data: [],
        columns: mockColumns,
        loading: true
      }
    })
    
    expect(wrapper.text()).toContain('加载中...')
    expect(wrapper.find('.loading-spinner').exists()).toBe(true)
  })

  it('shows empty state when no data', () => {
    const wrapper = mount(DataTable, {
      props: {
        data: [],
        columns: mockColumns,
        loading: false
      }
    })
    
    expect(wrapper.text()).toContain('暂无数据')
    expect(wrapper.find('.empty-state').exists()).toBe(true)
  })

  it('displays custom empty text', () => {
    const customEmptyText = '没有找到项目'
    const wrapper = mount(DataTable, {
      props: {
        data: [],
        columns: mockColumns,
        emptyText: customEmptyText
      }
    })
    
    expect(wrapper.text()).toContain(customEmptyText)
  })

  it('shows search input when searchable', () => {
    const wrapper = mount(DataTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        searchable: true
      }
    })
    
    const searchInput = wrapper.find('.search-input')
    expect(searchInput.exists()).toBe(true)
  })

  it('hides search input when not searchable', () => {
    const wrapper = mount(DataTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        searchable: false
      }
    })
    
    const searchInput = wrapper.find('.search-input')
    expect(searchInput.exists()).toBe(false)
  })

  it('emits search event when typing in search input', async () => {
    const wrapper = mount(DataTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        searchable: true
      }
    })
    
    const searchInput = wrapper.find('.search-input')
    await searchInput.setValue('项目A')
    await searchInput.trigger('input')
    
    expect(wrapper.emitted('search')).toBeTruthy()
    expect(wrapper.emitted('search')?.[0]).toEqual(['项目A'])
  })

  it('shows sortable columns with sort icons', () => {
    const wrapper = mount(DataTable, {
      props: {
        data: mockData,
        columns: mockColumns
      }
    })
    
    const sortableHeaders = wrapper.findAll('th.sortable')
    expect(sortableHeaders).toHaveLength(2) // name and createdAt are sortable
    
    sortableHeaders.forEach(header => {
      expect(header.find('.sort-icon').exists()).toBe(true)
    })
  })

  it('emits sort event when clicking sortable column', async () => {
    const wrapper = mount(DataTable, {
      props: {
        data: mockData,
        columns: mockColumns
      }
    })
    
    const sortableHeader = wrapper.find('th.sortable')
    await sortableHeader.trigger('click')
    
    expect(wrapper.emitted('sort')).toBeTruthy()
    expect(wrapper.emitted('sort')?.[0]).toEqual(['name', 'asc'])
  })

  it('emits row click event when clicking on row', async () => {
    const wrapper = mount(DataTable, {
      props: {
        data: mockData,
        columns: mockColumns
      }
    })
    
    const firstRow = wrapper.findAll('tbody tr')[0]
    await firstRow.trigger('click')
    
    expect(wrapper.emitted('rowClick')).toBeTruthy()
    expect(wrapper.emitted('rowClick')?.[0]).toEqual([mockData[0], 0])
  })

  it('shows pagination when enabled', () => {
    const wrapper = mount(DataTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        pagination: true,
        total: 10
      }
    })
    
    expect(wrapper.find('.pagination-controls').exists()).toBe(true)
    expect(wrapper.find('.pagination-info').exists()).toBe(true)
  })

  it('hides pagination when disabled', () => {
    const wrapper = mount(DataTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        pagination: false
      }
    })
    
    expect(wrapper.find('.pagination-controls').exists()).toBe(false)
  })

  it('emits page change event when clicking pagination', async () => {
    const wrapper = mount(DataTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        pagination: true,
        total: 20,
        pageSize: 10
      }
    })
    
    const nextButton = wrapper.find('.pagination-btn:last-child')
    await nextButton.trigger('click')
    
    expect(wrapper.emitted('pageChange')).toBeTruthy()
    expect(wrapper.emitted('pageChange')?.[0]).toEqual([2])
  })

  it('shows selection checkboxes when selectable', () => {
    const wrapper = mount(DataTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        selectable: true
      }
    })
    
    const checkboxes = wrapper.findAll('.checkbox')
    expect(checkboxes.length).toBeGreaterThan(0)
    expect(wrapper.find('.select-column').exists()).toBe(true)
  })

  it('emits selection change event when selecting rows', async () => {
    const wrapper = mount(DataTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        selectable: true
      }
    })
    
    const firstCheckbox = wrapper.findAll('.checkbox')[1] // Skip header checkbox
    await firstCheckbox.trigger('change')
    
    expect(wrapper.emitted('selectionChange')).toBeTruthy()
  })

  it('displays custom title when provided', () => {
    const title = '项目列表'
    const wrapper = mount(DataTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        title
      }
    })
    
    expect(wrapper.text()).toContain(title)
  })

  it('uses custom row key function', () => {
    const customRowKey = (record: any) => `custom-${record.id}`
    const wrapper = mount(DataTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        rowKey: customRowKey
      }
    })
    
    // 验证自定义行键是否被正确使用
    expect(wrapper.exists()).toBe(true)
  })
})
