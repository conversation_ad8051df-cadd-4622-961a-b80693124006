<template>
  <div class="data-table">
    <!-- 表格头部工具栏 -->
    <div class="table-header" v-if="showHeader">
      <div class="table-title">
        <h3 v-if="title">{{ title }}</h3>
      </div>
      <div class="table-actions">
        <!-- 搜索框 -->
        <div class="search-box" v-if="searchable">
          <input
            type="text"
            v-model="searchQuery"
            :placeholder="searchPlaceholder"
            @input="handleSearch"
            class="search-input"
          />
          <svg class="search-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
          </svg>
        </div>
        <!-- 自定义操作按钮 -->
        <slot name="toolbar-actions"></slot>
      </div>
    </div>

    <!-- 表格内容 -->
    <div class="table-container">
      <table class="table">
        <thead>
          <tr>
            <th v-if="selectable" class="select-column">
              <input
                type="checkbox"
                :checked="isAllSelected"
                @change="handleSelectAll"
                class="checkbox"
              />
            </th>
            <th
              v-for="column in columns"
              :key="column.key"
              :style="{ width: column.width }"
              :class="{ sortable: column.sortable }"
              @click="column.sortable && handleSort(column.key)"
            >
              <div class="th-content">
                <span>{{ column.title }}</span>
                <svg
                  v-if="column.sortable"
                  class="sort-icon"
                  :class="getSortClass(column.key)"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path d="M5 12l5-5 5 5H5z" />
                </svg>
              </div>
            </th>
            <th v-if="hasActions" class="actions-column">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="loading" class="loading-row">
            <td :colspan="totalColumns" class="loading-cell">
              <div class="loading-spinner">
                <div class="spinner"></div>
                <span>加载中...</span>
              </div>
            </td>
          </tr>
          <tr v-else-if="filteredData.length === 0" class="empty-row">
            <td :colspan="totalColumns" class="empty-cell">
              <div class="empty-state">
                <svg class="empty-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                </svg>
                <p>{{ emptyText }}</p>
              </div>
            </td>
          </tr>
          <tr
            v-else
            v-for="(record, index) in paginatedData"
            :key="getRowKey(record, index)"
            :class="{ selected: selectedRows.includes(getRowKey(record, index)) }"
            @click="handleRowClick(record, index)"
          >
            <td v-if="selectable" class="select-column">
              <input
                type="checkbox"
                :checked="selectedRows.includes(getRowKey(record, index))"
                @change="handleRowSelect(record, index)"
                @click.stop
                class="checkbox"
              />
            </td>
            <td v-for="column in columns" :key="column.key">
              <div class="cell-content">
                <slot
                  :name="`cell-${column.key}`"
                  :record="record"
                  :value="getColumnValue(record, column.key)"
                  :index="index"
                >
                  <span v-if="column.render">
                    <component
                      :is="'span'"
                      v-html="column.render(getColumnValue(record, column.key), record)"
                    ></component>
                  </span>
                  <span v-else>{{ getColumnValue(record, column.key) }}</span>
                </slot>
              </div>
            </td>
            <td v-if="hasActions" class="actions-column">
              <div class="action-buttons">
                <slot name="actions" :record="record" :index="index"></slot>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页器 -->
    <div class="table-footer" v-if="pagination && !loading">
      <div class="pagination-info">
        显示 {{ paginationInfo.start }} - {{ paginationInfo.end }} 条，共 {{ total }} 条
      </div>
      <div class="pagination-controls">
        <button
          :disabled="currentPage === 1"
          @click="handlePageChange(currentPage - 1)"
          class="pagination-btn"
        >
          上一页
        </button>
        <span class="page-numbers">
          <button
            v-for="page in visiblePages"
            :key="page"
            :class="{ active: page === currentPage }"
            @click="handlePageChange(page)"
            class="page-btn"
          >
            {{ page }}
          </button>
        </span>
        <button
          :disabled="currentPage === totalPages"
          @click="handlePageChange(currentPage + 1)"
          class="pagination-btn"
        >
          下一页
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, useSlots } from 'vue'
import type { TableColumn } from '@/types/devops'

interface Props {
  data: any[]
  columns: TableColumn[]
  loading?: boolean
  title?: string
  searchable?: boolean
  searchPlaceholder?: string
  selectable?: boolean
  pagination?: boolean
  pageSize?: number
  total?: number
  emptyText?: string
  rowKey?: string | ((record: any) => string | number)
  showHeader?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  searchable: true,
  searchPlaceholder: '搜索...',
  selectable: false,
  pagination: true,
  pageSize: 10,
  total: 0,
  emptyText: '暂无数据',
  rowKey: 'id',
  showHeader: true
})

const emit = defineEmits<{
  search: [query: string]
  sort: [key: string, order: 'asc' | 'desc']
  pageChange: [page: number]
  rowClick: [record: any, index: number]
  selectionChange: [selectedRows: any[]]
}>()

// 响应式数据
const searchQuery = ref('')
const currentPage = ref(1)
const sortKey = ref('')
const sortOrder = ref<'asc' | 'desc'>('asc')
const selectedRows = ref<(string | number)[]>([])

// 获取slots
const slots = useSlots()

// 计算属性
const hasActions = computed(() => !!slots.actions)

const totalColumns = computed(() => {
  let count = props.columns.length
  if (props.selectable) count++
  if (hasActions.value) count++
  return count
})

const filteredData = computed(() => {
  let result = [...props.data]
  
  // 排序
  if (sortKey.value) {
    result.sort((a, b) => {
      const aVal = getColumnValue(a, sortKey.value)
      const bVal = getColumnValue(b, sortKey.value)
      
      if (aVal < bVal) return sortOrder.value === 'asc' ? -1 : 1
      if (aVal > bVal) return sortOrder.value === 'asc' ? 1 : -1
      return 0
    })
  }
  
  return result
})

const paginatedData = computed(() => {
  if (!props.pagination) return filteredData.value
  
  const start = (currentPage.value - 1) * props.pageSize
  const end = start + props.pageSize
  return filteredData.value.slice(start, end)
})

const totalPages = computed(() => {
  if (!props.pagination) return 1
  return Math.ceil((props.total || filteredData.value.length) / props.pageSize)
})

const visiblePages = computed(() => {
  const pages = []
  const total = totalPages.value
  const current = currentPage.value
  
  if (total <= 7) {
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    if (current <= 4) {
      for (let i = 1; i <= 5; i++) {
        pages.push(i)
      }
      pages.push('...', total)
    } else if (current >= total - 3) {
      pages.push(1, '...')
      for (let i = total - 4; i <= total; i++) {
        pages.push(i)
      }
    } else {
      pages.push(1, '...')
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i)
      }
      pages.push('...', total)
    }
  }
  
  return pages
})

const paginationInfo = computed(() => {
  const start = (currentPage.value - 1) * props.pageSize + 1
  const end = Math.min(currentPage.value * props.pageSize, props.total || filteredData.value.length)
  return { start, end }
})

const isAllSelected = computed(() => {
  return paginatedData.value.length > 0 && 
         paginatedData.value.every(record => selectedRows.value.includes(getRowKey(record, 0)))
})

// 方法
const getRowKey = (record: any, index: number): string | number => {
  if (typeof props.rowKey === 'function') {
    return props.rowKey(record)
  }
  return record[props.rowKey] || index
}

const getColumnValue = (record: any, key: string) => {
  return key.split('.').reduce((obj, k) => obj?.[k], record)
}

const getSortClass = (key: string) => {
  if (sortKey.value !== key) return ''
  return sortOrder.value === 'asc' ? 'sort-asc' : 'sort-desc'
}

const handleSearch = () => {
  emit('search', searchQuery.value)
}

const handleSort = (key: string) => {
  if (sortKey.value === key) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortKey.value = key
    sortOrder.value = 'asc'
  }
  emit('sort', key, sortOrder.value)
}

const handlePageChange = (page: number | string) => {
  if (typeof page === 'number' && page !== currentPage.value) {
    currentPage.value = page
    emit('pageChange', page)
  }
}

const handleRowClick = (record: any, index: number) => {
  emit('rowClick', record, index)
}

const handleRowSelect = (record: any, index: number) => {
  const key = getRowKey(record, index)
  const selectedIndex = selectedRows.value.indexOf(key)
  
  if (selectedIndex > -1) {
    selectedRows.value.splice(selectedIndex, 1)
  } else {
    selectedRows.value.push(key)
  }
  
  emitSelectionChange()
}

const handleSelectAll = () => {
  if (isAllSelected.value) {
    selectedRows.value = []
  } else {
    selectedRows.value = paginatedData.value.map((record, index) => getRowKey(record, index))
  }
  
  emitSelectionChange()
}

const emitSelectionChange = () => {
  const selectedRecords = paginatedData.value.filter((record, index) => 
    selectedRows.value.includes(getRowKey(record, index))
  )
  emit('selectionChange', selectedRecords)
}

// 监听数据变化，重置分页
watch(() => props.data, () => {
  currentPage.value = 1
  selectedRows.value = []
})
</script>

<style scoped>
.data-table {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
}

.table-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200;
}

.table-title h3 {
  @apply text-lg font-semibold text-gray-900 m-0;
}

.table-actions {
  @apply flex items-center gap-3;
}

.search-box {
  @apply relative;
}

.search-input {
  @apply pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none;
  width: 250px;
}

.search-icon {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400;
}

.table-container {
  @apply overflow-x-auto;
}

.table {
  @apply w-full border-collapse;
}

.table th {
  @apply px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200;
}

.table th.sortable {
  @apply cursor-pointer hover:bg-gray-100 transition-colors;
}

.th-content {
  @apply flex items-center gap-1;
}

.sort-icon {
  @apply w-4 h-4 text-gray-400 transition-transform;
}

.sort-icon.sort-asc {
  @apply text-blue-500;
}

.sort-icon.sort-desc {
  @apply text-blue-500 transform rotate-180;
}

.table td {
  @apply px-4 py-3 border-b border-gray-200;
}

.table tbody tr {
  @apply hover:bg-gray-50 transition-colors;
}

.table tbody tr.selected {
  @apply bg-blue-50;
}

.select-column {
  @apply w-12;
}

.actions-column {
  @apply w-32;
}

.checkbox {
  @apply w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500;
}

.cell-content {
  @apply text-sm text-gray-900;
}

.action-buttons {
  @apply flex items-center gap-2;
}

.loading-row,
.empty-row {
  @apply hover:bg-transparent;
}

.loading-cell,
.empty-cell {
  @apply text-center py-8;
}

.loading-spinner {
  @apply flex items-center justify-center gap-2 text-gray-500;
}

.spinner {
  @apply w-5 h-5 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin;
}

.empty-state {
  @apply flex flex-col items-center gap-2 text-gray-500;
}

.empty-icon {
  @apply w-12 h-12 text-gray-300;
}

.table-footer {
  @apply flex items-center justify-between p-4 border-t border-gray-200;
}

.pagination-info {
  @apply text-sm text-gray-700;
}

.pagination-controls {
  @apply flex items-center gap-2;
}

.pagination-btn {
  @apply px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors;
}

.page-numbers {
  @apply flex items-center gap-1;
}

.page-btn {
  @apply w-8 h-8 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors;
}

.page-btn.active {
  @apply bg-blue-500 text-white border-blue-500 hover:bg-blue-600;
}
</style>
